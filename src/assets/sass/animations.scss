$loading-pulse-background-color: #ececec;

@keyframes loadingPulse {
    0% {
        background-color: rgba($loading-pulse-background-color, 1);
    }
    50% {
        background-color: rgba($loading-pulse-background-color, 0.5);
    }
    100% {
        background-color: rgba($loading-pulse-background-color, 1);
    }
}

.lift-up,
.lift-down {
    animation-duration: 1s;
    animation-fill-mode: both;
}

.lift-up {
    animation-name: liftUp;
}

.lift-down {
    animation-name: liftDown;
}

@keyframes liftUp {
    from {
        transform: translateY(0);
    }

    to {
        transform: translateY(-55px);
    }
}

@keyframes liftDown {
    from {
        transform: translateY(-55px);
    }

    to {
        transform: translateY(0);
    }
}

.avl-pulse-ring {
    pointer-events: none;
    border-radius: inherit;
    animation: avlPulseAnim 1.5s infinite ease-out;
}

@keyframes avlPulseAnim {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(2);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}
