@use './variables' as *;

.title-search-registry-select {
    &.mat-select {
        display: flex;
        align-items: center;
        width: 262px;
        height: 32px;
        padding: 0 16px;
        border-radius: 2px;
        background-color: #f4f4f5;
    }

    & .mat-select-arrow {
        color: #96969e !important;
    }
}

.mat-select-panel {
    &.expanded {
        max-height: 290px;
    }

    &.search-registry-select-panel {
        min-width: calc(100% + 24px) !important;
        transform: translateX(4px) !important;
    }

    & .mat-option {
        background-color: transparent;

        &.mat-selected:not(.mat-option-multiple) {
            background-color: transparent;
            color: $black;
        }
    }
}

.mat-option:hover:not(.mat-option-disabled),
.mat-option:focus:not(.mat-option-disabled) {
    background-color: transparent;
}

.input-select {
    border-radius: 4px;
    background: #f4f4f5;

    span.mat-form-field-label-wrapper label {
        height: 28px;

        color: #96969e !important;
        font-family: Arial, serif;
        font-size: 16px;
        line-height: 18px;
    }

    & .mat-form-field-infix,
    & .mat-form-field-wrapper {
        width: 100%;
    }

    & .mat-form-field-wrapper {
        padding: 6px 16px 6px 14px;
    }

    & .mat-form-field-infix {
        border-top: none;
        padding: 0;
    }

    & .mat-select-trigger {
        height: 28px;

        & .mat-select-value {
            font-size: 16px;
            line-height: 18px;
        }
    }
}

.cdk-overlay-container .cdk-overlay-pane .input-select__panel {
    position: absolute;
    top: 34px;
    left: 24px;

    border-radius: 4px !important;
    background-color: white;
    box-shadow: 0 4px 8px 0 rgba(38, 50, 56, 0.24);

    min-width: calc(100% + 30px) !important;
    max-width: calc(100% + 30px) !important;
    border-top-style: none;
}

.cdk-overlay-container .cdk-overlay-pane .input-select__panel.input-select__panel_above {
    bottom: 7px;
    top: unset;
}

.filed-copies-filter.input-select__panel {
    top: 18px !important;

    .mat-checkbox-inner-container,
    .mat-pseudo-checkbox {
        width: 14px;
        height: 14px;
    }

    .mat-option {
        padding: 0 8px;
        height: 26px;
    }

    .select-panel__search {
        height: 30px;
    }

    .select-panel__controls {
        height: 31px !important;
    }

    .mat-select-trigger .mat-select-value {
        font-family: Arial, sans-serif;
        font-weight: 400;
        font-size: 10px;
        line-height: 14px;
    }

    .mat-input-element,
    .mat-option-text {
        font-size: 12px;
        line-height: 14px;
    }

    .mat-option-text {
        text-transform: capitalize;
    }

    mat-pseudo-checkbox.mat-pseudo-checkbox-checked::after,
    .select-panel__checkbox.mat-checkbox-checked .mat-checkbox-background::after {
        background-size: 10px;
    }
}
