@import '~assets/sass/variables';

.avl-ui-tour.shepherd-element {
    border-radius: 8px;
}

.avl-ui-tour .shepherd-content .shepherd-header {
    font-family: 'ArialBold', sans-serif;
    background: none;
    padding: 8px 12px 4px 12px;

    & .shepherd-title {
        font-size: 16px;
        line-height: 140%;
        color: rgba(76, 82, 99, 0.5);

        & strong {
            color: rgba(76, 82, 99, 1);
        }
    }

    & span {
        display: block;
        line-height: 18px;
        color: rgba(73, 83, 107, 1);
    }
}

.avl-ui-tour .shepherd-content {
    & .shepherd-text {
        font-family: 'Arial', sans-serif;
        font-size: 14px;
        padding: 8px 12px 22px 12px;
        color: rgba(150, 150, 158, 1);
    }
}

.avl-ui-tour .shepherd-button {
    border-radius: 5px;
    font-size: 14px;
    font-family: 'ArialBold', sans-serif;

    &.shepherd-button-primary {
        background-color: rgba(73, 83, 107, 0.5);
        color: white;

        &:hover {
            background-color: rgba(73, 83, 107, 0.35);
        }
    }

    &.shepherd-button-secondary {
        background-color: transparent;
        color: rgba(196, 196, 201, 1);

        &:hover {
            background-color: transparent;
        }
    }

    &.skip-button {
        margin-right: auto;
        padding: 0;
        font-family: 'ArialBold', sans-serif;
        line-height: 18px;
        color: rgba(76, 82, 99, 1);

        &:hover {
            color: rgba(76, 82, 99, 0.7);
        }
    }
}

.info-label-wrapper {
    position: relative;

    & .info-label {
        position: absolute;
        top: -10px;
        right: -10px;
        z-index: 100;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        color: #748ece;
        cursor: pointer;
    }
}
