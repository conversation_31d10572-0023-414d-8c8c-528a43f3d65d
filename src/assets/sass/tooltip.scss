.mat-tooltip {
    max-width: 166px !important;
    background: rgba(38, 50, 56, 0.9);
    padding: 12px !important;
    margin: 8px !important;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.4px;

    &__bright-without-opacity {
        background-color: #4c5263;
        border-radius: 5px !important;
        padding: 8px 12px !important;
    }
}

.tooltip {
    &__nowrap {
        white-space: nowrap;
    }

    &__max-w330 {
        max-width: 330px !important;
    }

    &__max-w400 {
        max-width: 400px !important;
    }

    &__p8 {
        padding: 8px !important;
    }

    &__rounded4 {
        border-radius: 4px !important;
    }

    &__gray-blue-bg {
        background-color: #39464b;
    }

    &__text14px {
        font-size: 14px;
        line-height: 18px;
    }

    &__move-left-72-up-7 {
        transform: translate(-72px, -7px) !important;
    }

    &__move-left-104-up-7 {
        transform: translate(-104px, -7px) !important;
    }

    &__move-left-112 {
        transform: translateX(-112px) !important;
    }

    &__w-200-move-left-80 {
        transform: translateX(-80px) !important;
        max-width: 200px !important;
        min-width: 200px !important;
    }

    &__min-w-200 {
        min-width: 200px !important;
    }

    &__max-content {
        width: max-content;
    }
}

.searches-tooltip {
    width: 260px;
    min-width: 260px;
    padding: 20px 16px !important;

    color: white;
    font-family: Arial, serif;
    font-size: 14px;
    line-height: 18px;

    background-color: #4c5263;
    border-radius: 4px;
}

@keyframes tooltip-show {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 0.5;
        transform: scale(0.99);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}
