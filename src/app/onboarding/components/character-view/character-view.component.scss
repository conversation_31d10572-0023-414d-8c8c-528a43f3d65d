@import '~assets/sass/variables';

.character-view {
    position: relative;
    z-index: 1002;

    &__lottie-container {
        overflow: hidden;
    }

    &__lottie {
        display: block;
        transform: translate(0, 0);
        transition: all 0.3s ease 1s;

        &.left-bottom.hide {
            transform: translate(-100%, 100%);
            visibility: hidden;
        }

        &.right-bottom.hide {
            transform: translate(100%, 100%);
            visibility: hidden;
        }

        &.right-top {
            margin-top: 35%;

            &.hide {
                transform: translate(100%, 0);
            }
        }

        &.left-top {
            margin-top: 35%;

            &.hide {
                transform: translate(-100%, 0);
            }
        }
    }

    &__card {
        position: absolute;
        padding: 24px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0px 16px 30px rgba(0, 0, 0, 0.15);
        animation-name: slideIn;
        animation-duration: 1s;
        animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.335);

        &.hide {
            opacity: 0;
            animation-name: slideOut;
            animation-duration: 1s;
            animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.335);
        }

        &.left-bottom,
        &.left-center {
            width: 264px;
            top: 90px;
            right: -182px;

            &:after {
                top: 30px;
                left: 0px;
                transform: rotate(45deg);
            }
        }

        &.right-bottom {
            width: 282px;
            top: -135px;
            right: 32px;

            &:after {
                bottom: -24px;
                right: 45px;
                transform: rotate(-45deg);
            }
        }

        &.right-top {
            width: 282px;
            bottom: 70px;
            right: 180px;

            &:after {
                right: -24px;
                top: 60px;
                transform: rotate(-135deg);
            }
        }

        &.left-top {
            width: 282px;
            bottom: 70px;
            left: 180px;

            &:after {
                left: 0;
                top: 32px;
                transform: rotate(45deg);
            }
        }

        &:after {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            box-sizing: border-box;
            border: 12px solid #fff;
            border-color: transparent transparent #fff #fff;
            transform-origin: 0 0;
            box-shadow: (-6px) 6px 10px -4px rgba(0, 0, 0, 0.15);
        }
    }

    &__card-text {
        color: $black;
        font-size: 14px;
        line-height: 18px;
        margin-bottom: 8px;

        &:last-of-type {
            margin-bottom: 0;
        }
    }

    &__card-link {
        color: $blue;
    }

    &__card-action {
        margin-top: 16px;
        border: none;
        font-size: 12px;
        text-align: start;
        line-height: 16px;
        letter-spacing: -0.28px;
        background-color: #fff;
        color: $blue;
    }
}

@keyframes slideIn {
    0% {
        opacity: 0;
    }

    80% {
        opacity: 0;
        transform: scale(0.8);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideOut {
    0% {
        opacity: 1;
    }

    80% {
        opacity: 1;
        transform: scale(1);
    }

    100% {
        opacity: 0;
        transform: scale(0.8);
    }
}
