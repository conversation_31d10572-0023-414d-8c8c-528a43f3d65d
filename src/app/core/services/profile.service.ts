import { Injectable } from '@angular/core';

import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { App, Logo, Plan } from '@core/types';
import { environment } from '@env/environment';
import { ProfileApi } from '@api';
import { AvailToolKey } from '@enums';
import {
    defaultMapSearchMinZoomFeatureVisibility,
    defaultSamCircleMaxRadiusKm,
    defaultSamCircleMinRadiusKm,
    defaultSamPolygonMaxAreaM2,
} from '../../titles/constants/land-registry-search.constants';

@Injectable({
    providedIn: 'root',
})
export class ProfileService {
    public isHmlrEnabled$: BehaviorSubject<boolean>;
    public isRosEnabled$: BehaviorSubject<boolean>;
    public isSamEnabled$: BehaviorSubject<boolean>;
    public isMapSearchEnabled$: BehaviorSubject<boolean>;
    public mapSearchMinZoomFeatureVisibility$: BehaviorSubject<number>;
    public samPolygonMaxAreaM2$: BehaviorSubject<number>;
    public samCircleMaxRadiusKm$: BehaviorSubject<number>;
    public samCircleMinRadiusKm$: BehaviorSubject<number>;
    public isImanageEnabled$: BehaviorSubject<boolean>;
    public isSearchesEnabled$: BehaviorSubject<boolean>;
    public isFiledCopiesUploadEnabled$: BehaviorSubject<boolean>;
    public isOneClickFiledCopiesEnabled$: BehaviorSubject<boolean>;
    public isAlpsEnabled$: BehaviorSubject<boolean>;
    public isTeamworkEnabled$: BehaviorSubject<boolean>;
    public isLongTermStorageEnabled$: BehaviorSubject<boolean>;
    public logo$: BehaviorSubject<Logo>;
    public apps$: BehaviorSubject<App[] | null>;
    public plan$: BehaviorSubject<Plan>;

    constructor(
        private readonly profileApi: ProfileApi,
    ) {
        this.initState();
    }

    public clear(): void {
        this.initState();
    }

    public ensureProfile(): Observable<string> {
        return this.profileApi.ensureProfile();
    }

    public shouldShowOnboarding(app: AvailToolKey | undefined = AvailToolKey.title): Observable<boolean> {
        if (app !== AvailToolKey.title) {
            return of(false);
        }

        return this.profileApi.shouldShowOnboarding()
            .pipe(
                map((response) => (response || 'true').trim() === 'true'),
            );
    }

    public onboardingShown(): Observable<string> {
        return this.profileApi.onboardingShown();
    }

    public getLogo(): Observable<Logo> {
        return this.logo$.asObservable();
    }

    public loadConfig(): void {
        this.profileApi.getConfig()
            .pipe(
                tap((config) => {
                    this.isImanageEnabled$.next(environment.forceImanageEnabled || !!config.imanageConfig?.imanageUrl);
                    this.isHmlrEnabled$.next(config.isHmlrEnabled);
                    this.isRosEnabled$.next(environment.isRosEnabled && config.isRosEnabled);
                    this.isSearchesEnabled$.next(config.searches);
                    this.isFiledCopiesUploadEnabled$.next(environment.isFiledCopiesUploadEnabled && config.isFiledCopiesUploadEnabled);
                    this.isOneClickFiledCopiesEnabled$.next(environment.isOneClickFiledCopiesPurchaseEnabled && config.isOneClickFiledCopiesPurchaseEnabled);
                    this.isSamEnabled$.next(environment.isSamEnabled && config.isSamEnabled);
                    this.isMapSearchEnabled$.next(environment.isMapSearchEnabled && config.isMapSearchEnabled);
                    this.isAlpsEnabled$.next(config.isAlpsEnabled);
                    this.isTeamworkEnabled$.next(config.isTeamworkEnabled);
                    this.isLongTermStorageEnabled$.next(config.isLongTermStorageEnabled);
                    this.mapSearchMinZoomFeatureVisibility$.next(config.mapSearchMinZoomFeatureVisibility);
                    this.samPolygonMaxAreaM2$.next(config.samPolygonMaxAreaM2);
                    this.samCircleMaxRadiusKm$.next(config.samCircleMaxRadiusKm);
                    this.samCircleMinRadiusKm$.next(config.samCircleMinRadiusKm);
                    this.plan$.next(config.plan);
                    this.apps$.next(config.apps ?? []);

                    if (config.theme?.icon && config.theme?.iconWidth && config.theme?.iconHeight) {
                        this.logo$.next({ ...config.theme, defaultLogo: false });
                    }
                }),
                catchError(() => of(false)),
            )
            .subscribe();
    }

    private initState(): void {
        this.isHmlrEnabled$ = new BehaviorSubject<boolean>(false);
        this.isRosEnabled$ = new BehaviorSubject<boolean>(false);
        this.isOneClickFiledCopiesEnabled$ = new BehaviorSubject<boolean>(false);
        this.isSamEnabled$ = new BehaviorSubject<boolean>(false);
        this.isMapSearchEnabled$ = new BehaviorSubject<boolean>(false);
        this.mapSearchMinZoomFeatureVisibility$ = new BehaviorSubject<number>(defaultMapSearchMinZoomFeatureVisibility);
        this.samPolygonMaxAreaM2$ = new BehaviorSubject<number>(defaultSamPolygonMaxAreaM2);
        this.samCircleMaxRadiusKm$ = new BehaviorSubject<number>(defaultSamCircleMaxRadiusKm);
        this.samCircleMinRadiusKm$ = new BehaviorSubject<number>(defaultSamCircleMinRadiusKm);
        this.isImanageEnabled$ = new BehaviorSubject<boolean>(false);
        this.isSearchesEnabled$ = new BehaviorSubject(false);
        this.isFiledCopiesUploadEnabled$ = new BehaviorSubject(false);
        this.isAlpsEnabled$ = new BehaviorSubject(false);
        this.isTeamworkEnabled$ = new BehaviorSubject(false);
        this.isLongTermStorageEnabled$ = new BehaviorSubject(false);
        this.logo$ = new BehaviorSubject<Logo>({ defaultLogo: true });
        this.apps$ = new BehaviorSubject<App[] | null>(null);
        this.plan$ = new BehaviorSubject<Plan>({
            organisation: '',
            plan: '',
            apps: [],
            matterNumber: {
                pattern: '.+',
                errorMessage: 'Matter Number is required',
            },
            projectName: {
                pattern: '.+',
                errorMessage: 'Project Name is required',
            },
        });
    }
}
