import { Injectable, OnDestroy } from '@angular/core';
import { Query, Store } from '@datorama/akita';
import { BehaviorSubject, Observable, of, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, switchMap, tap } from 'rxjs/operators';
import { LoggerService } from './logger.service';

type StoreConfig<T> = {
    storeName: string;
    store: Store<T>;
    query: Query<T>;
    select: (state: T) => Partial<T>;
    debounceMs?: number;
};

type StoredWithMeta<T> = {
    data: T;
    updatedAt: number;
};

@Injectable({ providedIn: 'root' })
export class ProjectScopedStorageService implements OnDestroy {
    private readonly localStoragePrefix = 'folder-';
    private readonly ttlMs = 30 * 24 * 60 * 60 * 1000; // 30 days
    private readonly defaultListeningDebounceMs = 1000;
    private readonly currentProjectId$ = new BehaviorSubject<string | null>(null);
    private readonly subscriptionsStore = new Map<string, Subscription>();
    private readonly storeConfigMap = new Map<string, StoreConfig<any>>();

    constructor(
        private readonly log: LoggerService,
    ) {
        this.clearExpiredProjectStorage(this.localStoragePrefix, this.ttlMs);
        this.listenToProjectIdChanges();
    }

    public registerStore<T>(config: StoreConfig<T>): void {
        const { storeName, store } = config;
        if (this.subscriptionsStore.has(storeName)) {
            this.log.warn(`[ProjectScopedStorage] Store '${storeName}' already registered — skipping.`);
            return;
        }

        this.storeConfigMap.set(storeName, config);

        this.currentProjectId$
            .pipe(
                filter((id): id is string => !!id),
                switchMap((projectId) => {
                    const localKey = this.getStorageKey(storeName, projectId);
                    const savedRaw = localStorage.getItem(localKey);

                    if (savedRaw) {
                        try {
                            const parsed = JSON.parse(savedRaw) as StoredWithMeta<Partial<T>>;
                            store.update(parsed.data);
                        } catch (e) {
                            this.log.warn(`[ProjectScopedStorage] Failed to parse localStorage for '${localKey}'`, e);
                        }
                    }

                    this.unsubscribeStore(storeName);

                    return this.watchStore(projectId, config);
                }),
            )
            .subscribe((sub) => {
                this.subscriptionsStore.set(storeName, sub);
            });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll();
        this.subscriptionsStore.clear();
        this.storeConfigMap.clear();
    }

    public updateProjectId(folderId: string): void {
        this.currentProjectId$.next(folderId || null);
    }

    public clear(): void {
        this.unsubscribeAll();
        this.subscriptionsStore.clear();
        this.storeConfigMap.clear();
        this.clearProjectStorage();
    }

    private watchStore<T>(projectId: string, config: StoreConfig<T>): Observable<Subscription> {
        const { storeName, query, select } = config;
        const debounceMs = this.storeConfigMap.get(storeName)?.debounceMs ?? this.defaultListeningDebounceMs;
        const sub = query
            .select()
            .pipe(
                debounceTime(debounceMs),
                map((state) => select(state)),
                distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
                tap((selectedState) => {
                    const key = this.getStorageKey(storeName, projectId);
                    const wrapped: StoredWithMeta<Partial<T>> = {
                        data: selectedState,
                        updatedAt: Date.now(),
                    };
                    localStorage.setItem(key, JSON.stringify(wrapped));
                }),
            )
            .subscribe();

        return of(sub);
    }

    private getStorageKey(storeName: string, projectId: string): string {
        return `${this.localStoragePrefix ?? ''}${projectId}-${storeName}`;
    }

    private clearExpiredProjectStorage(localStoragePrefix: string, ttlMs: number): void {
        if (ttlMs <= 0) {
            return;
        }

        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            const item = localStorage.getItem(key);

            if (key && key.startsWith(localStoragePrefix) && item) {
                const record = JSON.parse(item);

                if (!record.updatedAt) {
                    return;
                }

                const isExpired = this.isExpired(record.updatedAt, ttlMs);
                if (isExpired) {
                    localStorage.removeItem(key);
                }
            }
        }
    }

    private clearProjectStorage(): void {
        const localStorageItemsAmount = localStorage.length;
        const keysToRemove = [];

        for (let i = 0; i < localStorageItemsAmount; i++) {
            const key = localStorage.key(i);

            if (key && key.startsWith(this.localStoragePrefix)) {
                keysToRemove.push(key);
            }
        }

        keysToRemove.forEach((key) => localStorage.removeItem(key));
    }

    private isExpired(updatedAt: number, ttlMs: number): boolean {
        if (ttlMs <= 0) {
            return false;
        }

        return Date.now() - updatedAt > ttlMs;
    }

    private unsubscribeStore(storeName: string): void {
        this.subscriptionsStore.get(storeName)?.unsubscribe();
        this.subscriptionsStore.delete(storeName);
    }

    private unsubscribeAll(): void {
        this.subscriptionsStore.forEach((sub) => sub.unsubscribe());
    }

    private listenToProjectIdChanges(): void {
        this.currentProjectId$
            .pipe(distinctUntilChanged())
            .subscribe((projectId) => {
                if (!projectId) {
                    this.unsubscribeAll();
                    this.subscriptionsStore.clear();

                    return;
                }

                this.storeConfigMap.forEach((config, storeName) => {
                    const localKey = this.getStorageKey(storeName, projectId);
                    const savedRaw = localStorage.getItem(localKey);

                    if (savedRaw) {
                        try {
                            const parsed = JSON.parse(savedRaw) as StoredWithMeta<Partial<any>>;
                            config.store.update(parsed.data);
                        } catch (e) {
                            this.log.warn(`[ProjectScopedStorage] Failed to parse localStorage for '${localKey}'`, e);
                        }
                    }

                    this.unsubscribeStore(storeName);

                    this.watchStore(projectId, config).subscribe((sub) => {
                        this.subscriptionsStore.set(storeName, sub);
                    });
                });
            });
    }
}
