/* eslint-disable @typescript-eslint/naming-convention */
import { App, ImanageParams, ImanageParamsToMap, Logo, LogoToMap, mapImanageParams, mapLogo, Plan } from '@core/types';
import {
    defaultMapSearchMinZoomFeatureVisibility,
    defaultSamCircleMaxRadiusKm,
    defaultSamCircleMinRadiusKm,
    defaultSamPolygonMaxAreaM2,
} from '../../titles/constants/land-registry-search.constants';

export type ProfileConfig = {
    apps: App[];
    isHmlrEnabled: boolean;
    isRosEnabled: boolean;
    isOneClickFiledCopiesPurchaseEnabled: boolean;
    isSamEnabled: boolean;
    isMapSearchEnabled: boolean;
    mapSearchMinZoomFeatureVisibility: number;
    samPolygonMaxAreaM2: number;
    samCircleMaxRadiusKm: number;
    samCircleMinRadiusKm: number;
    imanageConfig?: ImanageParams;
    isNewHomePage: boolean;
    plan: Plan;
    theme?: Logo;
    searches: boolean;
    isFiledCopiesUploadEnabled: boolean;
    isAlpsEnabled: boolean;
}

export type ProfileConfigToMap = {
    'apps': App[];
    'hmlr-enabled': boolean;
    'is-ros-enabled': boolean;
    'is-one-click-filed-copies-purchase-enabled': boolean;
    'is-sim-searches-enabled': boolean;
    'is-map-search-enabled': boolean;
    'map-search-min-zoom-feature-visibility': number;
    'sam-search-polygon-max-area-m2': number;
    'sam-search-circle-max-radius-km': number;
    'sam-search-circle-min-radius-km': number;
    'imanage-config'?: ImanageParamsToMap;
    'new-home-page': boolean;
    'plan': Plan;
    'theme'?: LogoToMap;
    'searches': boolean;
    'is-filed-copies-upload-enabled': boolean;
    'is-alps-projects-enabled': boolean;
}

export function mapProfileConfig(config: ProfileConfigToMap): ProfileConfig {
    return {
        apps: config['apps'],
        isHmlrEnabled: config['hmlr-enabled'],
        isRosEnabled: config['is-ros-enabled'],
        isOneClickFiledCopiesPurchaseEnabled: config['is-one-click-filed-copies-purchase-enabled'] ?? false,
        isSamEnabled: config['is-sim-searches-enabled'] ?? false,
        isMapSearchEnabled: config['is-map-search-enabled'] ?? false,
        mapSearchMinZoomFeatureVisibility: config['map-search-min-zoom-feature-visibility']
            ?? defaultMapSearchMinZoomFeatureVisibility,
        samPolygonMaxAreaM2: config['sam-search-polygon-max-area-m2'] ?? defaultSamPolygonMaxAreaM2,
        samCircleMaxRadiusKm: config['sam-search-circle-max-radius-km'] ?? defaultSamCircleMaxRadiusKm,
        samCircleMinRadiusKm: config['sam-search-circle-min-radius-km'] ?? defaultSamCircleMinRadiusKm,
        imanageConfig: config['imanage-config'] && mapImanageParams(config['imanage-config']),
        isNewHomePage: config['new-home-page'],
        plan: config['plan'],
        theme: config['theme'] && mapLogo(config['theme']),
        searches: config['searches'] ?? false,
        isFiledCopiesUploadEnabled: config['is-filed-copies-upload-enabled'] ?? false,
        isAlpsEnabled: config['is-alps-projects-enabled'] ?? false,
    };
}
