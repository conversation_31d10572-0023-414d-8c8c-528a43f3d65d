import { IFileUploaderOptions } from '../../blocks/file-uploader';

export const maxFileSizeMb = 100;

export const fileUploaderOptions: IFileUploaderOptions = {
    uploader: {
        fileAlias: 'file',
        autoUpload: false,
        simultaneousUpload: true,
        simultaneousUploadQuantityLimit: 20,
    },
    filePicker: {
        allowedMultipleFiles: true,
        allowedMimeClass: ['pdf'],
        allowedFileExtension: ['pdf'],
        maxFileSize: maxFileSizeMb * 1024 * 1024,
    },
    queue: {
        removeAfterUploadComplete: false,
    },
};
