import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { AlertOkDialogComponent } from '@shared/components/dialogs/alert-ok-dialog/alert-ok-dialog.component';
import { REPORT_GENERATION_IN_PROGRESS } from '@constants';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { LoggerService } from '@services';


@Injectable()
export class ApiInterceptor implements HttpInterceptor {
    private dialogInstance: MatDialogRef<AlertOkDialogComponent> | null = null;

    constructor(
        private readonly dialog: MatDialog,
        private readonly log: LoggerService,
    ) {
    }

    public intercept(request: HttpRequest<any>, next: HttpHand<PERSON>): Observable<HttpEvent<any>> {
        return next.handle(request)
            .pipe(
                catchError((error) => {
                    const isHttpErrorResponse = error instanceof HttpErrorResponse;

                    if (isHttpErrorResponse && error.status === 423) {
                        this.log.info('Report generation in progress error', error);
                        this.showErrorDialog();
                    }

                    return throwError(() => error);
                }),
            );
    }

    public showErrorDialog(): void {
        if (!this.dialogInstance?.componentInstance) {
            this.dialogInstance = this.dialog.open(AlertOkDialogComponent, REPORT_GENERATION_IN_PROGRESS);
            this.dialogInstance.afterClosed().subscribe(() => this.dialogInstance = null);
        }
    }
}
