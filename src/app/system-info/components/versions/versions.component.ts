import { Component, OnInit } from '@angular/core';
import { catchError, of } from 'rxjs';
import packageInfo from '../../../../../package.json';
import { HealthApi } from '../../api/health.api';

@Component({
    selector: 'avl-versions',
    templateUrl: './versions.component.html',
    styleUrls: ['./versions.component.scss'],
})
export class VersionsComponent implements OnInit {
    public readonly headers = ['Entity Name', 'Version'];
    public readonly appVersion = packageInfo.version;
    public readonly noValueDefaultMessage = 'Unavailable';

    public data: { [key: string]: string }[] = [];

    constructor(private readonly healthApi: HealthApi) {}

    public ngOnInit(): void {
        this.initializeData();
        this.fetchBackendVersion();
    }

    private initializeData(): void {
        this.data = [
            {
                [this.headers[0]]: 'App version',
                [this.headers[1]]: this.appVersion,
            },
            {
                [this.headers[0]]: 'Backend version',
                [this.headers[1]]: null,
            },
        ];
    }

    private fetchBackendVersion(): void {
        this.healthApi.getBackendVersion()
            .pipe(
                catchError(() => of(this.noValueDefaultMessage))
            )
            .subscribe((version) => {
                this.data[1][this.headers[1]] = version;
            });
    }
}
