import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { VersionsComponent } from './components/versions/versions.component';
import { RouterModule } from '@angular/router';
import { systemInfoRoutes } from './system-info.routing';
import { HealthApi } from './api/health.api';


@NgModule({
    declarations: [
        VersionsComponent,
    ],
    imports: [
        CommonModule,
        RouterModule.forChild(systemInfoRoutes),
    ],
    providers: [
        HealthApi
    ]
})
export class SystemInfoModule {
}
