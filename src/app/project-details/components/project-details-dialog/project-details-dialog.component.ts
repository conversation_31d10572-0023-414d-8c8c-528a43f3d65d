import { Component, Inject, OnD<PERSON>roy, OnInit, Optional } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ProfileService } from '@services';
import { ProjectDialogResponseType } from '../../enums/project-dialog-response-type.enum';
import { ProjectDetailsDialogResponse } from '../../types/project-details-dialog-response.type';
import { ProjectDetails } from '../../types/project-details.type';
import { animate, state, style, transition, trigger } from '@angular/animations';

@Component({
    selector: 'avl-project-details-dialog',
    templateUrl: './project-details-dialog.component.html',
    styleUrls: ['./project-details-dialog.component.scss'],
    animations: [
        trigger('expandCollapse', [
            state('collapsed', style({
                height: '0',
                opacity: '0',
                overflow: 'hidden',
                padding: '0',
                margin: '0',
            })),
            state('expanded', style({
                height: '*',
                opacity: '1',
            })),
            transition('collapsed <=> expanded', animate('200ms ease-in-out')),
        ]),
    ],
})
export class ProjectDetailsDialogComponent implements OnInit, OnDestroy {
    public folderDetailsForm: FormGroup;
    public isAlpsEnabled$: Observable<boolean>;
    public errorMessage = {
        projectName: 'Project Name is required',
        matterNumber: 'Matter Number is required',
        alpsClientEmail: 'Client Email is required',
        alpsClientEmailInvalid: 'Client Email should be a valid email',
    };

    private readonly destroy$ = new Subject<void>();

    constructor(
        @Optional()
        @Inject(MAT_DIALOG_DATA)
        private readonly data: ProjectDetails,
        private readonly dialogRef: MatDialogRef<ProjectDetailsDialogComponent, ProjectDetailsDialogResponse>,
        private readonly formBuilder: FormBuilder,
        private readonly profileService: ProfileService,
    ) {
    }

    public ngOnInit(): void {
        this.isAlpsEnabled$ = this.profileService.isAlpsEnabled$.asObservable();
        this.setupForm();
        this.handleEscapePress();
        this.handleAlpsStatusChange();
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    public close(): void {
        if (!this.dialogRef.disableClose) {
            this.dialogRef.close({ event: ProjectDialogResponseType.cancel });
        }
    }

    public confirm(): void {
        const folderDetails: ProjectDetails = this.folderDetailsForm.value;
        const response: ProjectDetailsDialogResponse = {
            event: ProjectDialogResponseType.confirm,
            data: folderDetails,
        };

        this.dialogRef.close(response);
    }

    public isProjectNameDirtyAndInvalid(): boolean {
        return this.isDirtyAndInvalid('projectName');
    }

    public isMatterNumberDirtyAndInvalid(): boolean {
        return this.isDirtyAndInvalid('matterNumber');
    }

    public isAlpsClientEmailDirtyAndInvalid(): boolean {
        return this.isDirtyAndInvalid('alpsClientEmail');
    }

    public isAlpsNoteDirtyAndInvalid(): boolean {
        return this.isDirtyAndInvalid('alpsNote');
    }

    public getAlpsFieldsState(): string {
        return this.folderDetailsForm.get('isAlps').value ? 'expanded' : 'collapsed';
    }

    public getErrorMessage(): string {
        let errorMessage = '';

        if (this.isProjectNameDirtyAndInvalid()) {
            errorMessage = this.errorMessage.projectName;
        } else if (this.isMatterNumberDirtyAndInvalid()) {
            errorMessage = this.errorMessage.matterNumber;
        } else if (this.isAlpsClientEmailDirtyAndInvalid()) {
            const emailControl = this.folderDetailsForm.get('alpsClientEmail');
            if (emailControl.hasError('required')) {
                errorMessage = this.errorMessage.alpsClientEmail;
            } else if (emailControl.hasError('email')) {
                errorMessage = this.errorMessage.alpsClientEmailInvalid;
            }
        }

        return errorMessage;
    }

    private isDirtyAndInvalid(fieldName: string): boolean {
        const control = this.folderDetailsForm.get(fieldName);

        if (!control) {
            return false;
        }

        return control.dirty && control.invalid;
    }

    private setupForm(): void {
        const planValue = this.profileService.plan$.getValue();
        const folderNamePattern = planValue.projectName.pattern;
        const folderMatterNumberPattern = planValue.matterNumber.pattern;
        const isAlpsEnabled = this.profileService.isAlpsEnabled$.getValue();

        const nameValidator = [Validators.required];
        const matterNumberValidator = [Validators.required];

        if (folderNamePattern) {
            nameValidator.push(Validators.pattern(folderNamePattern));
            this.errorMessage.projectName = planValue.projectName.errorMessage;
        }

        if (folderMatterNumberPattern) {
            matterNumberValidator.push(Validators.pattern(folderMatterNumberPattern));
            this.errorMessage.matterNumber = planValue.matterNumber.errorMessage;
        }

        this.folderDetailsForm = this.formBuilder.group({
            projectName: [this.data.projectName, nameValidator],
            matterNumber: [this.data.matterNumber, matterNumberValidator],
            isAlps: [isAlpsEnabled ? (this.data.isAlps || false) : false],
            alpsClientEmail: [isAlpsEnabled ? this.data.alpsClientEmail : null],
            alpsNote: [isAlpsEnabled ? this.data.alpsNote : null],
        });
    }

    private handleAlpsStatusChange(): void {
        this.folderDetailsForm.get('isAlps').valueChanges
            .pipe(takeUntil(this.destroy$))
            .subscribe((isAlps) => {
                const alpsClientEmailControl = this.folderDetailsForm.get('alpsClientEmail');
                const alpsNoteControl = this.folderDetailsForm.get('alpsNote');

                if (isAlps) {
                    alpsClientEmailControl.setValidators([Validators.required, Validators.email]);
                } else {
                    alpsClientEmailControl.clearValidators();
                    alpsNoteControl.clearValidators();
                    alpsClientEmailControl.setValue(null);
                    alpsNoteControl.setValue(null);
                }

                alpsClientEmailControl.updateValueAndValidity();
                alpsNoteControl.updateValueAndValidity();
            });
    }

    private handleEscapePress(): void {
        this.dialogRef.keydownEvents()
            .pipe(takeUntil(this.destroy$))
            .subscribe((event) => {
                if (event.key === 'Escape') {
                    this.close();
                }
            });
    }
}
