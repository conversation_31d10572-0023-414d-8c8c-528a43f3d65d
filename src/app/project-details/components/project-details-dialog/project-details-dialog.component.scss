@import '~assets/sass/variables';

::ng-deep .project-details-dialog {
    width: 446px;

    .mat-dialog-title {
        text-align: center;
        margin-bottom: 44px;
    }

    .mat-dialog-container {
        position: relative;
        padding: 0;
    }

    .mat-dialog-content {
        padding: 0 44px 20px;
    }

    .form-control {
        font-size: 14px;
        padding-top: 21px;
        padding-bottom: 21px;
    }

    .form-group .form-icon {
        left: 20px;
        opacity: 1;
    }

    .mat-dialog-actions {
        padding: 0;

        .avl-btn {
            border-radius: 0;
            padding-top: 21px;
            line-height: 18px;
            padding-bottom: 21px;

            &--dark-purple {
                &:disabled {
                    background-color: $light-gray;
                }
            }
        }
    }

    &__error-message {
        display: none;
        margin-bottom: 10px;
        color: $bright-red;
        font-size: 12px;
        line-height: 16px;

        &.has-error {
            display: block;
        }
    }

    .form-group {
        &.has-error {
            .form-control {
                box-shadow: inset 0 0 0 1px $bright-red;
                border-color: $bright-red;
            }
        }

        &::after {
            top: 20px;
            right: 15px;
            width: 22px;
            height: 22px;
            background: url('~assets/icons/icon_alert_error.svg') no-repeat center;
        }

        .error-message {
            color: $bright-red;
        }
    }

    .alps-checkbox {
        margin: 15px 0;

        &__input {
            font-size: 14px;
        }
    }

    .alps-fields {
        overflow: hidden;
    }
}

.email-icon,
.note-icon {
    width: 20px;
    height: 20px;
    color: $light-gray;
}
