<button
    class="dialog__close"
    (click)="close()"
>
    <mat-icon
        class="close-icon"
        svgIcon="close"
    ></mat-icon>
</button>
<h4 mat-dialog-title>Enter Project Details</h4>
<form
    [formGroup]="folderDetailsForm"
    (submit)="confirm()"
>
    <mat-dialog-content>
        <div
            class="form-group"
            [class.has-error]="isProjectNameDirtyAndInvalid()"
        >
            <input
                type="text"
                class="form-control"
                name="projectName"
                placeholder="Project Name"
                autocomplete="off"
                formControlName="projectName"
            />
            <mat-icon
                class="form-icon"
                svgIcon="user"
            ></mat-icon>
        </div>
        <div
            class="form-group"
            [class.has-error]="isMatterNumberDirtyAndInvalid()"
        >
            <input
                type="text"
                class="form-control"
                name="matterNumber"
                placeholder="Client / Matter Number"
                autocomplete="off"
                formControlName="matterNumber"
            />
            <mat-icon
                class="form-icon"
                svgIcon="file-chart-rounded"
            ></mat-icon>
        </div>

        <ng-container *ngIf="isAlpsEnabled$ | async">
            <div class="form-group alps-checkbox">
                <mat-checkbox
                    formControlName="isAlps"
                    class="alps-checkbox__input"
                >
                    ALPS
                </mat-checkbox>
            </div>

            <div
                [@expandCollapse]="getAlpsFieldsState()"
                class="alps-fields"
            >
                <div
                    class="form-group"
                    [class.has-error]="isAlpsClientEmailDirtyAndInvalid()"
                >
                    <input
                        type="email"
                        class="form-control"
                        name="alpsClientEmail"
                        placeholder="ALPS Client Email"
                        autocomplete="off"
                        formControlName="alpsClientEmail"
                    />
                    <mat-icon
                        class="form-icon email-icon"
                        svgIcon="alternate-email"
                    ></mat-icon>
                </div>
                <div
                    class="form-group"
                    [class.has-error]="isAlpsNoteDirtyAndInvalid()"
                >
                    <input
                        type="text"
                        class="form-control"
                        name="alpsNote"
                        placeholder="ALPS Note"
                        autocomplete="off"
                        formControlName="alpsNote"
                    />
                    <mat-icon
                        class="form-icon note-icon"
                        svgIcon="mist"
                    ></mat-icon>
                </div>
            </div>
        </ng-container>

        <div
            class="project-details-dialog__error-message"
            [class.has-error]="!!getErrorMessage()"
        >
            <p>{{ getErrorMessage() }}</p>
        </div>
    </mat-dialog-content>
    <div mat-dialog-actions>
        <button
            class="avl-btn avl-btn--dark-purple avl-btn--wide"
            [disabled]="folderDetailsForm.invalid"
        >
            Let’s Go
        </button>
    </div>
</form>
