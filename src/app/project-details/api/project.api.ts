import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ProjectAppPath } from '../enums/project-app-type.enum';
import { ProjectDetails } from '../types/project-details.type';
import { map } from 'rxjs/operators';
import { createInitialDetails } from '../stores/project-details/project-details.store';

@Injectable()
export class ProjectApi {

    constructor(
        private readonly http: HttpClient,
    ) {
    }

    public create(appPath: ProjectAppPath): Observable<string> {
        return this.http.post(`/api/${appPath}/folder`, null, { responseType: 'text' });
    }

    public getDetails(appPath: ProjectAppPath, projectId: string): Observable<ProjectDetails> {
        return this.http.get<ProjectDetails>(`api/${appPath}/project/${projectId}?suppress404=true`)
            .pipe(
                map((details) => !details.found ? createInitialDetails() : details),
            );
    }

    public updateDetails(appPath: ProjectAppPath, projectId: string, details: ProjectDetails): Observable<void> {
        return this.http.put<void>(`api/${appPath}/project/${projectId}`, details);
    }
}
