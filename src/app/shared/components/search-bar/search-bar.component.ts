import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
    selector: 'avl-search-bar',
    templateUrl: './search-bar.component.html',
    styleUrls: ['./search-bar.component.scss'],
})
export class SearchBarComponent {
    @Input()
    public placeholder: string;

    @Output()
    public searchChanged = new EventEmitter<string>();


    public onSearch(value: string): void {
        this.searchChanged.emit(value);
    }
}
