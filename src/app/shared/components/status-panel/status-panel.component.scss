@import '~assets/sass/mixins';

::ng-deep .panel {
    --border-color: grey;
    --background-color: white;

    width: 100%;
    border: 1px solid var(--border-color, grey);
    border-radius: 8px;
    background-color: var(--background-color, white);

    .mat-expansion-panel-body {
        padding-right: 1px;
    }

    .mat-expansion-panel-header {
        display: flex;
        height: auto;
        align-items: center;
        border-bottom: none;
        padding: 5px 14px;

        .mat-expansion-indicator {
            right: 12px;
            top: 6px;
            width: 20px;
            height: 20px;

            &:before {
                background-image: url('~assets/icons/icon_dropdown_dark.svg');
                width: 20px;
                height: 20px;
            }
        }
    }

    .panel__header-content {
        display: flex;
        gap: 8px;
        align-items: center;
        width: 100%;
        height: 24px;
    }

    .panel__icon {
        width: 18px;
    }

    .panel__header-text {
        font-weight: 700;
        color: #4c5263;
        font-size: 14px;
    }

    .panel__details {
        padding: 5px 13px 5px 14px;
        max-height: 200px;

        .panel__item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2px;
            border-bottom: 1px solid #0000000d;

            &:last-child {
                border-bottom: none;
            }

            .panel__dot {
                margin: 0 9px;
            }

            .panel__details-text {
                font-size: 14px;
                line-height: 18px;
            }

            .panel__code {
                color: #4c5263;
                white-space: nowrap;
            }

            .panel__message {
                color: #96969e;
                text-align: end;
                padding-right: 12px;
            }
        }
    }
}

.text-truncate {
    @include multi-line-ellipsis(1);
}
