import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subscription, SubscriptionLike, tap } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

@Component({
    selector: 'avl-search',
    templateUrl: './search.component.html',
    styleUrls: ['./search.component.scss'],
})
export class SearchComponent implements OnInit, OnDestroy {
    public searchControl = new FormControl();

    @Input()
    public placeholder: string;

    @Output()
    public searchChanged = new EventEmitter<string>();

    @Output()
    public inputValueChanged = new EventEmitter<string>();

    private searchControlSubscription: SubscriptionLike = Subscription.EMPTY;

    @Input()
    public set query(value: string) {
        this.searchControl.setValue(value);
    }

    public ngOnInit(): void {
        this.setupFormListeners();
    }

    public ngOnDestroy(): void {
        this.searchControlSubscription.unsubscribe();
    }

    private setupFormListeners(): void {
        this.searchControlSubscription = this.searchControl.valueChanges
            .pipe(
                tap((value) => this.inputValueChanged.emit(value)),
                debounceTime(500)
            )
            .subscribe((result: string) => this.searchChanged.emit(result));
    }
}
