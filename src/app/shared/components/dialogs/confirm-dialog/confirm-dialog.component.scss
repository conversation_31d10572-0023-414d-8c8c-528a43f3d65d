@import '~assets/sass/variables';

:host {
    display: block;
    padding: 64px 32px 48px;
}

.confirm-dialog {
    &__icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 64px;
        height: 64px;
        margin: 0 auto;
        border-radius: 50%;
        background-repeat: no-repeat;
        background-position: center;

        &.red {
            background-color: $red;
        }

        &.bright-red {
            background-color: $bright-red;
        }

        &.blue {
            background-color: $blue;
        }

        &.green {
            background-color: $dark-green;
        }

        &.yellow {
            background-color: #c1a850;
        }

        &.dark-purple {
            background-color: $dark-purple;
        }

        &.light-yellow {
            background-color: #f3ba0e;
        }

        &.notice-yellow {
            background-color: $notice-yellow;
        }

        &.create-icon {
            background-size: 37px 37px;
            background-image: url('~assets/images/create-icon.png');
        }

        &.remove-icon {
            background-size: 27px 27px;
            background-image: url('~assets/images/remove-icon.png');
        }

        &.pound-icon {
            background-size: 27px 27px;
            background-image: url('~assets/images/pound-icon.png');
        }

        &.land-registry-analysis {
            width: 100px;
            height: 100px;
            background-size: 100px;
            background-image: url('~assets/icons/icon_land_registry_analysis.svg');
            background-color: transparent;
            border-radius: 0;
        }

        .icon-source {
            width: 26px;
            height: 26px;
        }
    }

    &__content {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 24px auto 32px;
        text-align: center;
    }

    &__title {
        max-width: 260px;
        font-size: 16px;
        line-height: 21px;
    }

    &__subtitle {
        margin-top: 15px;
        color: $dark-gray;
        font-size: 12px;
    }

    &__button-group {
        display: flex;
        gap: 16px;
        justify-content: center;

        .avl-btn {
            min-width: 155px;

            &--green {
                background-color: $dark-green;
            }

            &--yellow {
                background-color: #c1a850;
            }

            &--light-yellow {
                background-color: #f3ba0e;
            }

            &--notice-yellow {
                background-color: $notice-yellow;

                &:hover {
                    background-color: rgb($notice-yellow, 0.7);
                }
            }
        }
    }
}

.buttons_small-side-paddings {
    .confirm-dialog__button-group .avl-btn {
        padding: 15px;
    }
}
