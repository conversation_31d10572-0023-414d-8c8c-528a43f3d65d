@use 'assets/sass/variables' as *;

:host {
    display: block;
    padding: 64px 32px 48px;
}

.confirm-dialog {
    &__icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 64px;
        height: 64px;
        margin: 0 auto;
        border-radius: 50%;
        background-repeat: no-repeat;
        background-position: center;

        &.red {
            background-color: $red;
        }

        &.blue {
            background-color: $blue;
        }

        &.create-icon {
            background-size: 37px 37px;
            background-image: url('~assets/images/create-icon.png');
        }

        &.remove-icon {
            background-size: 27px 27px;
            background-image: url('~assets/images/remove-icon.png');
        }

        .icon-source {
            width: 26px;
            height: 26px;
        }
    }

    &__title {
        max-width: 200px;
        margin: 24px auto 26px;
        font-size: 16px;
        text-align: center;
    }

    &__subtitle {
        margin-bottom: 25px;
        color: $dark-gray;
        font-size: 12px;
        text-align: center;
    }

    &__button-group {
        display: flex;
        align-items: center;
        justify-content: center;

        .avl-btn {
            margin: 0 8px;
            width: 155px;
        }
    }
}
