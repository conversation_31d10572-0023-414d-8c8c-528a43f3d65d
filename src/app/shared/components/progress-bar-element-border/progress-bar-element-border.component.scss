@use 'assets/sass/variables' as *;

.container {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius);
    background-color: transparent;
}

#content-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.line {
    position: absolute;
    background-color: var(--border-color);
}

.horizontal {
    left: calc(var(--border-radius) - var(--border-width));
    width: calc(100% - var(--border-radius) * 2 + var(--border-width) * 2);
    height: var(--border-width);
}

.vertical {
    top: calc(var(--border-radius) - var(--border-width));
    width: var(--border-width);
    height: calc(100% - var(--border-radius) * 2 + var(--border-width) * 2);
}

.top {
    top: calc(var(--border-width) * -1);
    transform-origin: right;
}

.half-top-segment {
    width: calc((100% - var(--border-radius) * 2) / 2 + var(--border-width) * 2);
}

.half-top-segment__right {
    right: calc(var(--border-radius) - var(--border-width));
    left: initial;
    animation: shrinkWidth var(--line-w-half-animation-time) linear var(--1-line-animation-delay) forwards;
    animation-play-state: var(--play-state);
}

.half-top-segment__left {
    left: calc(var(--border-radius) - var(--border-width));
    animation: shrinkWidth var(--line-w-half-animation-time) linear var(--5-line-animation-delay) forwards;
    animation-play-state: var(--play-state);
}

.right {
    right: calc(var(--border-width) * -1);
    transform-origin: bottom;
    animation: shrinkHeight var(--line-h-animation-time) linear var(--2-line-animation-delay) forwards;
    animation-play-state: var(--play-state);
}

.bottom {
    bottom: calc(var(--border-width) * -1);
    transform-origin: left;
    animation: shrinkWidth var(--line-w-animation-time) linear var(--3-line-animation-delay) forwards;
    animation-play-state: var(--play-state);
}

.left {
    left: calc(var(--border-width) * -1);
    transform-origin: top;
    animation: shrinkHeight var(--line-h-animation-time) linear var(--4-line-animation-delay) forwards;
    animation-play-state: var(--play-state);
}

.corner {
    position: absolute;
    width: var(--corner-circle-diameter);
    height: var(--corner-circle-diameter);
    border-radius: var(--border-radius);
}

.corner::before {
    --color-and-angle: var(--border-color) var(--corner-angle);
    content: '';
    inset: 0;
    position: absolute;
    border-radius: inherit;
    background: conic-gradient(
        from var(--corner-start-angle),
        transparent 0deg,
        transparent var(--corner-angle),
        var(--color-and-angle),
        var(--border-color) 90deg,
        transparent 90deg
    );
    animation: cornerRotate var(--corner-animation-time) linear var(--corner-animation-delay) forwards;
    animation-play-state: var(--play-state);
}

.top-right {
    top: calc(var(--border-width) * -1);
    right: calc(var(--border-width) * -1);
    --corner-start-angle: 0deg;
    --corner-animation-delay: var(--1-corner-animation-delay);
}

.bottom-right {
    right: calc(var(--border-width) * -1);
    bottom: calc(var(--border-width) * -1);
    --corner-start-angle: 90deg;
    --corner-animation-delay: var(--2-corner-animation-delay);
}

.bottom-left {
    left: calc(var(--border-width) * -1);
    bottom: calc(var(--border-width) * -1);
    --corner-start-angle: 180deg;
    --corner-animation-delay: var(--3-corner-animation-delay);
}

.top-left {
    top: calc(var(--border-width) * -1);
    left: calc(var(--border-width) * -1);
    --corner-start-angle: 270deg;
    --corner-animation-delay: var(--4-corner-animation-delay);
}

.square-container::before {
    --color-and-angle: var(--border-color) var(--angle);
    content: '';
    position: absolute;
    top: calc(var(--border-width) * -1);
    left: calc(var(--border-width) * -1);
    right: calc(var(--border-width) * -1);
    bottom: calc(var(--border-width) * -1);
    border-radius: inherit;
    background: conic-gradient(from 0deg at 50% 50%, transparent var(--angle), var(--color-and-angle));
    animation: rotate calc(var(--animation-duration) + 100ms) var(--timing, linear);
    animation-play-state: var(--play-state);
}

@keyframes shrinkWidth {
    from {
        transform: scaleX(1);
    }
    to {
        transform: scaleX(0);
    }
}

@keyframes shrinkHeight {
    from {
        transform: scaleY(1);
    }
    to {
        transform: scaleY(0);
    }
}

@keyframes cornerRotate {
    to {
        --corner-angle: 90deg;
    }
}

@property --corner-angle {
    syntax: '<angle>';
    initial-value: 0deg;
    inherits: false;
}

@keyframes rotate {
    to {
        --angle: 360deg;
    }
}

@property --angle {
    syntax: '<angle>';
    initial-value: 0deg;
    inherits: false;
}

.disabled {
    &.container::before {
        display: none;
    }
}
