@use 'assets/sass/variables' as *;

.email-field-container {
    position: relative;
    width: 100%;
}

.email-chips-field {
    width: 100%;
}

.form-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 20px;
    z-index: 10;
    opacity: 0.6;
}

.email-icon {
    width: 20px;
    height: 20px;
    color: $light-gray;
}

::ng-deep .email-chips-field {
    .mat-input-element {
        caret-color: black;
        margin-left: 0;
        height: 30px;
        font-size: 14px;

        &::placeholder {
            color: $light-gray;
            opacity: 0.8;
        }
    }

    &.mat-form-field-appearance-outline {
        .mat-form-field-wrapper {
            padding-bottom: 0;
            margin: 0;
        }

        .mat-form-field-flex {
            margin: 0;
            padding: 0 15px 0 50px;
            min-height: 60px;
            border-radius: 5px;
        }

        .mat-form-field-outline {
            color: transparent;
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        &:hover .mat-form-field-outline {
            border-color: rgba($blue, 0.7);
            color: transparent;
        }

        &.mat-focused {
            .mat-form-field-outline {
                color: transparent;
            }

            .mat-form-field-outline-thick {
                box-shadow: inset 0 0 0 1px rgba($blue, 0.4);
                color: transparent;
            }

            .form-icon {
                opacity: 0;
            }
        }

        .mat-form-field-infix {
            border-top: 0;
            padding: 6px 0;
        }

        .mat-form-field-label-wrapper {
            top: -1.5em;
        }

        .mat-form-field-subscript-wrapper {
            padding: 0;
            margin-top: 2px;
        }
    }

    // Chip styles
    .mat-chip-list-wrapper {
        padding: 5px 0;
        margin: 0;
        min-height: 38px;
    }

    .mat-chip {
        background-color: #f0f3fa;
        color: $blue;
        border-radius: 4px;
        font-size: 14px;
        height: 30px;
        transition: background-color 0.2s ease;
        font-weight: 400;
        padding: 4px 10px;
        margin: 4px 8px 4px 0;

        &.mat-standard-chip:not(.mat-chip-disabled):active {
            box-shadow: none;
        }

        .mat-chip-ripple {
            display: none;
        }

        .mat-icon {
            color: rgba(0, 0, 0, 0.5);
            height: 18px;
            width: 18px;
            opacity: 0.6;
            transition: opacity 0.2s ease;
            margin-right: -4px;
            margin-left: 6px;

            &:hover {
                opacity: 1;
            }
        }
    }

    .mat-standard-chip {
        .mat-chip-remove.mat-icon {
            width: 18px;
            height: 18px;
        }

        &:hover::after {
            display: none;
        }

        &:focus::after {
            display: none;
        }
    }
}
