import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
    selector: 'avl-switch-toggle',
    templateUrl: './switch-toggle.component.html',
    styleUrls: ['./switch-toggle.component.scss'],
})
export class SwitchToggleComponent {
    @Input()
    public buttonId: string;

    @Input()
    public icon: string;

    @Input()
    public isActivated = false;

    @Output()
    public toggleChanged = new EventEmitter<boolean>();

    public onToggle(): void {
        this.isActivated = !this.isActivated;
        this.toggleChanged.emit(this.isActivated);
    }
}
