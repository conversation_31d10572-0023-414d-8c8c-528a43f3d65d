:host {
    display: flex;
}

.switch-toggle {
    display: inline-block;
    cursor: pointer;
    user-select: none;

    &__track {
        position: relative;
        width: 3em;
        height: 1.5em;
        background-color: #d6d7dd;
        border-radius: 0.75em;
        transition: background-color 0.3s ease;

        &--active {
            background-color: #748ece;
        }
    }

    &__icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        font-size: 0.75em;
        width: 1em;
        height: 1em;
        opacity: 0.6;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: opacity 0.3s ease;
        color: #ffffff;

        &--active {
            opacity: 1;
        }

        &--left {
            left: 0.375em;
        }

        &--right {
            right: 0.375em;
            color: #96969e;
        }
    }

    &__circle {
        position: absolute;
        top: 0.125em;
        left: 0.125em;
        width: 1.25em;
        height: 1.25em;
        background-color: #ffffff;
        border-radius: 50%;
        transition: transform 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

        &--active {
            transform: translateX(1.5em);
        }
    }

    &__check {
        font-size: 0.75em;
        width: 0.75em;
        height: 0.75em;
        color: #748ece;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
