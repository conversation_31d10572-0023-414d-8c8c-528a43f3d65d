.checkbox {
    &__border {
        border: 1px solid #b3b3b8;
        border-radius: 3px;
    }

    &__check-mark {
        position: absolute;
        inset: 1px;

        width: 12px;
        height: 12px;

        border: none;
        transform: unset;
        opacity: 1;
        background-image: url('~../../../../assets/icons/icon_check.svg');
        background-size: 12px;
        background-repeat: no-repeat;
    }
}

::ng-deep .select-panel__controls mat-pseudo-checkbox {
    display: none;
}

::ng-deep {
    & .input-select__panel {
        & mat-pseudo-checkbox {
            @extend .checkbox__border;

            &.mat-pseudo-checkbox-checked::after {
                @extend .checkbox__check-mark;
            }
        }
    }

    & .mat-option-text {
        color: #000113;
        font-family: Arial, serif;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
    }
}

::ng-deep .input-select {
    & .mat-select-trigger {
        gap: 10px;
    }

    &__trigger {
        color: #000113;
        gap: 10px;
    }
}

.input-select {
    &__trigger {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    &__icon {
        width: 16px;
        height: 16px;
    }
}

::ng-deep .select-panel__checkbox {
    & .mat-checkbox-inner-container:hover .mat-checkbox-persistent-ripple {
        opacity: 0;
    }

    & .mat-checkbox-background,
    & .mat-checkbox-frame {
        @extend .checkbox__border;
    }

    &.mat-checkbox-checked,
    &.mat-checkbox-indeterminate {
        & .mat-checkbox-background {
            background-color: #0c6ad9;
            border: none;
        }
    }

    &.mat-checkbox-checked {
        & .mat-checkbox-background::after {
            @extend .checkbox__check-mark;
            content: '';
            inset: 2px;
        }

        & .mat-checkbox-checkmark {
            display: none;
        }
    }
}

::ng-deep .select-panel__search .mat-input-element {
    caret-color: #b3b3b8;
    margin-top: 2px;
}

::ng-deep .select-panel__icon-remove.mat-icon {
    margin-right: 0 !important;

    width: 20px;
    height: 20px;
}

.select-panel {
    &__controls {
        position: relative;

        display: flex;
        flex-direction: column;
        align-items: flex-start;
        height: 41px !important;
    }

    &__search {
        display: flex;
        align-items: center;
        gap: 5px;
        width: 100%;
        height: 40px;
    }
}

.select-panel {
    &__buttons {
        display: flex;
        gap: 32px;
        padding: 6px 0;
    }
}

.divider {
    position: absolute;
    left: -16px;

    width: calc(100% + 32px);
    height: 1px;

    background-color: #f3f3f4;
}
