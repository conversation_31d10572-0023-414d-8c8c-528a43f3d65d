import { Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { AuthService } from '@auth/services/auth.service';
import { HttpQueryParamsService, ProfileService } from '@services';
import { OnboardingManageService } from 'app/onboarding/services';

import { environment } from '@env/environment';

import { BehaviorSubject, of, Subject } from 'rxjs';
import { takeUntil, tap } from 'rxjs/operators';
import { Logo } from '@core/types';
import { MatMenu } from '@angular/material/menu';
import { IntercomService } from '../../../core/services/intercom.service';
import { ProjectDetails } from '../../../project-details/types/project-details.type';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';

const defaultAvailLogo: Logo = {
    defaultLogo: true,
    icon: 'assets/images/dark-logo.png',
    iconWidth: 90,
    iconHeight: 24,
};

@Component({
    selector: 'avl-header',
    templateUrl: './header.component.html',
    styleUrls: ['./header.component.scss'],
    encapsulation: ViewEncapsulation.None,
})
export class HeaderComponent implements OnInit {

    @Input()
    public profileContextMenu?: MatMenu;

    @Input()
    public folderIsNotCreated: boolean;

    @Input()
    public logo: Logo;

    @Input()
    public homePagePath = 'home';

    @Input()
    public showHamburgerMenu = false;

    @Input()
    public showNotificationBell = false;

    @Input()
    public folderDetails: ProjectDetails;

    @Input()
    public isBellHighlighted$ = of(false);

    @Output()
    public newFolderCreated = new EventEmitter<void>();

    @Output()
    public onboardingShowed = new EventEmitter<void>();

    @Output()
    public notificationCenterOpened = new EventEmitter<void>();

    @Output()
    public sideNavToggled = new EventEmitter<void>();

    @Output()
    public projectDetailsOpened = new EventEmitter<void>();

    public logo$ = new BehaviorSubject(defaultAvailLogo);
    public isOnboarding = false;
    public isNewProjectBtnDisabled = false;
    public profileIconReference: Element;

    constructor(
        private readonly authService: AuthService,
        private readonly intercom: IntercomService,
        private readonly profileService: ProfileService,
        private readonly onboarding: OnboardingManageService,
        private readonly router: Router,
        private readonly route: ActivatedRoute,
        private readonly queryParamsService: HttpQueryParamsService,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
    ) {
    }

    public ngOnInit(): void {
        this.profileIconReference = document.getElementById('profile');
        this.addSubscriptions();
        this.loadLogo();
        this.intercom.load();
    }

    public isProviderSso(): boolean {
        return this.authService.isProviderSso();
    }

    public createNewFolder(): void {
        this.onboarding.closeActiveOverlay();
        this.newFolderCreated.emit();
    }

    public openNotificationCenter(): void {
        this.notificationCenterOpened.emit();
    }

    public async logout(): Promise<void> {
        const queryParams = this.queryParamsService.addRedirectToQueryParams({ isOtherParamsIncluded: false });

        await this.authService.logout();
        await this.router.navigate(['/login'], { queryParams });
    }

    public async logoutStay(): Promise<void> {
        await this.authService.logout();
    }

    public isEnvironmentProduction(): boolean {
        return environment.production;
    }

    public showOnboardingAgain(): void {
        this.onboarding.closeActiveOverlay();
        this.onboardingShowed.emit();
    }

    public goHome(): void {
        const routeSnapshot = this.route.snapshot;
        const folderId = routeSnapshot.queryParamMap.get('fid') || this.projectDetailsQuery.projectId;

        void this.router.navigate(
            [this.homePagePath],
            {
                queryParams: folderId
                    ? { fid: folderId }
                    : null,
            }
        );
    }

    private addSubscriptions(): void {
        const unsubscriber$ = new Subject<void>();
        this.onboarding.isOnboardingActive()
            .pipe(
                takeUntil(unsubscriber$),
                tap((isActive) => {
                    this.isOnboarding = isActive;

                    if (!isActive) {
                        unsubscriber$.next();
                        this.isNewProjectBtnDisabled = true;
                    }
                }),
            )
            .subscribe();

        this.onboarding.createProjectBtnDisabled$
            .pipe(
                takeUntil(unsubscriber$),
                tap((btnDisabled) => this.isNewProjectBtnDisabled = btnDisabled),
            )
            .subscribe();
    }

    private loadLogo(): void {
        this.profileService.getLogo()
            .subscribe((logo) => {
                if (logo.defaultLogo) {
                    this.logo$.next(this.logo || defaultAvailLogo);
                } else {
                    this.logo$.next(logo);
                }
            });
    }
}
