import {
    Directive,
    Input,
    OnD<PERSON>roy,
    OnInit,
    TemplateRef,
    ViewContainerRef,
} from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

@Directive({
    selector: '[avlPageTitle]',
})
export class PageTitleDirective implements OnInit, OnDestroy {
    private readonly subscription = new Subscription();
    private elseTemplateRef: TemplateRef<any> | null = null;
    private previousTitle: string | null = null;

    constructor(
        private readonly viewContainer: ViewContainerRef,
        private readonly templateRef: TemplateRef<any>,
        private readonly router: Router,
        private readonly route: ActivatedRoute,
    ) {
    }

    @Input()
    public set avlPageTitleElse(templateRef: TemplateRef<any> | null) {
        this.elseTemplateRef = templateRef;
    }

    public ngOnInit(): void {
        this.updateTitle();

        const sub = this.router.events
            .pipe(filter((event) => event instanceof NavigationEnd))
            .subscribe(() => {
                this.updateTitle();
            });

        this.subscription.add(sub);
    }

    public ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    private updateTitle(): void {
        let currentRoute = this.route;
        while (currentRoute.firstChild) {
            currentRoute = currentRoute.firstChild;
        }

        const pageTitle = currentRoute.snapshot.data['pageTitle'] || '';

        if (this.previousTitle === pageTitle) {
            return;
        }

        this.previousTitle = pageTitle;
        this.viewContainer.clear();

        if (pageTitle) {
            const options = {
                $implicit: pageTitle,
            };
            this.viewContainer.createEmbeddedView(this.templateRef, options);
        } else if (this.elseTemplateRef) {
            this.viewContainer.createEmbeddedView(this.elseTemplateRef);
        }
    }
}
