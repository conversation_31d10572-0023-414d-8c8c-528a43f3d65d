import { NEVER, Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router } from '@angular/router';
import { catchError, tap } from 'rxjs/operators';
import { DocumentStore } from '../states';
import { FolderApi } from '../api';
import { convertToMinimalDocument, FileMetadata } from '../types/file-metadata.type';
import { NotificationService } from '../components/notification-center/services/notification.service';
import { ProjectDetailsQuery } from '../../project-details/stores/project-details/project-details.query';
import { LoggerService } from '@services';
import { IResponseStatus } from '@core/types';
import { ProjectsService } from '../../project-details/services/projects.service';


@Injectable()
export class LeasesResolver implements Resolve<IResponseStatus<FileMetadata>> {

    constructor(
        private readonly documentStore: DocumentStore,
        private readonly folderApi: FolderApi,
        private readonly router: Router,
        private readonly notificationCenter: NotificationService,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly projectsService: ProjectsService,
        private readonly log: LoggerService,
    ) {
    }

    public resolve(route: ActivatedRouteSnapshot): Observable<IResponseStatus<FileMetadata>> {
        const folderId = route.queryParams.fid || this.projectDetailsQuery.projectId;

        if (folderId) {
            return this.updateFolderStatus(folderId);
        }
    }

    private updateFolderStatus(folderId: string): Observable<IResponseStatus<FileMetadata>> {
        return this.folderApi.getFileStatus(folderId)
            .pipe(
                tap((status) => {
                    const documents = status.documents
                        .filter((doc) => {
                            const isDocumentFailed = !doc.isAccepted || doc.isError;

                            if (isDocumentFailed) {
                                this.notificationCenter.highlightBell();
                            }

                            return !isDocumentFailed;
                        })
                        .map((doc) => convertToMinimalDocument(doc));

                    this.documentStore.set(documents);
                }),
                catchError((error) => {
                    this.log.warn('LeasesResolver error:', error);
                    this.projectsService.reset();
                    this.router.navigate(['lease']);

                    return NEVER;
                }),
            );
    }

}
