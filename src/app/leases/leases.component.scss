@use 'assets/sass/mixins' as *;
@use 'assets/sass/variables' as *;

:host {
    display: block;
    height: 100%;
}

.lease {
    &__sidenav {
        height: 100%;

        &.mat-drawer-container {
            background-color: $mid-gray;
        }
    }

    &__content {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 0 128px;
        margin: 0 auto;

        @include maxW(992px) {
            padding-right: $folder-lg-padding;
            padding-left: $folder-lg-padding;
        }

        @include maxW(768px) {
            padding-right: $folder-md-padding;
            padding-left: $folder-md-padding;
        }
    }
}
