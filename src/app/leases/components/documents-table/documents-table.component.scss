@use 'assets/sass/variables' as *;
@use 'assets/sass/mixins' as *;

$side-paddings: 52px;
$row-height: 50px;

:host {
    display: block;
    border-radius: 5px;
    position: relative;
    overflow: hidden;
}

.file-icon--width {
    min-width: 24px;
}

.file-name--width {
    width: 100%;
}

.file-type--width {
    min-width: 85px;
}

.upload-time--width {
    min-width: 100px;
}

.upload-date--width {
    min-width: 140px;
}

.size--width {
    min-width: 70px;
}

.pages--width {
    min-width: 60px;
}

.documents-table {
    &__container {
        height: 100%;
        background-color: #fff;
        border-radius: 5px;
        overflow: auto;

        &::-webkit-scrollbar {
            width: 5px;
        }

        &::-webkit-scrollbar-track {
            background-color: $gray;
        }

        &::-webkit-scrollbar-thumb {
            border-radius: 5px;
            background-color: $dark-gray;
        }
    }

    &__table {
        position: relative;
        width: 100%;
        border-spacing: 0;
        z-index: 10;
    }

    &__row {
        height: $row-height;
        font-size: 13px;
        color: #000113;

        &--loading {
            color: #4c5263;
        }
    }

    &__col {
        position: relative;
        border-bottom: 1px solid rgba($gray, 0.3);
        line-height: 16px;

        &:first-of-type {
            padding: 0 $side-paddings 0 30px;
        }

        &--head {
            color: $dark-gray;
            font-size: 10px;
            cursor: pointer;
            text-align: start;
            text-transform: uppercase;
        }

        &.file-icon {
            &:hover {
                .file-icon_regular {
                    display: none;
                }

                .file-icon__reorder {
                    display: block;
                }
            }
        }

        &.size {
            text-transform: uppercase;
        }

        &.progress-bar {
            width: 100%;
            padding-right: $side-paddings;
        }
    }

    &__col-remove {
        display: flex;
        padding: 0 21px;

        .remove-icon {
            width: 20px;
            height: 20px;
            opacity: 0.6;
            cursor: pointer;
            transition: opacity 0.2s ease;

            &:hover {
                opacity: 1;
            }
        }
    }
}

.file-icon__reorder {
    display: none;
    cursor: pointer;
}

.preview {
    gap: 10px;
    justify-content: space-around;
    padding: 0 10px;
    height: $row-height;
    border-bottom: 1px solid rgba($gray, 0.3);
}

.placeholder {
    height: $row-height;
}
