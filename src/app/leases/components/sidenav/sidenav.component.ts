import { Component, EventEmitter, Input, Output } from '@angular/core';
import { AvailToolKey } from '@enums';
import { Observable } from 'rxjs/internal/Observable';
import { SideNavCounters } from '../../types/side-nav-counters.type';
import { ProfileService } from '@services';

@Component({
    selector: 'avl-sidenav',
    templateUrl: './sidenav.component.html',
    styleUrls: ['./sidenav.component.scss'],
})
export class SidenavComponent {
    public readonly tools = AvailToolKey;

    @Input()
    public counters: Observable<SideNavCounters>;

    @Output()
    public createNewFolder = new EventEmitter();

    @Output()
    public closeSideNav = new EventEmitter();

    public isTeamworkEnabled$ = this.profile.isTeamworkEnabled$.asObservable();

    constructor(
        private readonly profile: ProfileService,
    ) {
    }

    public onCreateNewFolder(): void {
        this.createNewFolder.emit();
    }

    public close(): void {
        this.closeSideNav.emit();
    }
}
