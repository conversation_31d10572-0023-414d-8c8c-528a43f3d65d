@use 'assets/sass/variables' as *;

:host {
    display: flex;
    flex: 1;
    flex-direction: column;
}

.team-projects-table-container {
    ::ng-deep &.collection__table-container .loading-table {
        &__row {
            height: 57px !important;
        }
    }

    ::ng-deep .mat-column {
        &-projectName {
            width: 40%;
            white-space: nowrap;
        }

        &-owner {
            width: 35%;
        }

        &-lastOpenedAt {
            width: 15%;
            min-width: 160px;
        }

        &-leases {
            width: 8%;
            min-width: 60px;
            text-align: center;

            .mat-sort-header-container {
                justify-content: center;
            }
        }

        &-download {
            width: 1%;
            min-width: 104px;
            text-align: right;

            &.mat-header-cell {
                min-width: 136px;
            }

            .avl-btn {
                &--dark-blue {
                    &:disabled {
                        background-color: $light-gray;
                    }
                }
            }
        }

        &-open {
            width: 1%;
            padding-left: 0;
            min-width: 88px;
            text-align: right;

            &.mat-header-cell {
                min-width: 112px;
            }
        }
    }
}

.project-info-column {
    display: flex;
    flex-direction: column;
    white-space: nowrap;
}

.project-name {
    font-size: 14px;
    line-height: 22px;
}

.matter-number {
    font-size: 12px;
    line-height: 20px;
    opacity: 0.5;
}

.download-btn,
.open-project-btn {
    padding: 8px 10px;
    height: 32px;
    width: 100%;
    border-radius: 3px;
}

.download-btn {
    width: 104px;

    &:disabled {
        opacity: 1;
    }
}

.avl-btn--dark-blue {
    &:disabled {
        background-color: $light-gray;
    }
}

.open-project-btn {
    width: 88px;
}
