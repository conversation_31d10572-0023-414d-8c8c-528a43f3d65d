import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { LeaseProject } from '../../types/lease-project.type';
import { TeamProjectsService } from '../../../../../teamwork-common/stores/team-projects.service';
import { TeamProjectsQuery } from '../../../../../teamwork-common/stores/team-projects.query';
import { BaseTeamProjectsPageComponent } from '../../../../../teamwork-common/components/base-team-projects-page.component';
import { CompileService } from '../../../../states';
import { TeamProjectsTableComponent } from '../team-projects-table/team-projects-table.component';

@Component({
    selector: 'avl-team-projects-page',
    templateUrl: './team-projects-page.component.html',
    styleUrls: ['./team-projects-page.component.scss'],
})
export class TeamProjectsPageComponent extends BaseTeamProjectsPageComponent<LeaseProject> implements OnInit, OnD<PERSON>roy {
    @ViewChild(TeamProjectsTableComponent, { static: false })
    public tableComponent: TeamProjectsTableComponent;


    constructor(
        teamProjectsService: TeamProjectsService<LeaseProject>,
        teamProjectsQuery: TeamProjectsQuery<LeaseProject>,
        private readonly router: Router,
        private readonly reportService: CompileService,
    ) {
        super(teamProjectsService, teamProjectsQuery);
    }

    public ngOnInit(): void {
        super.ngOnInit();
    }

    public ngOnDestroy(): void {
        super.ngOnDestroy();
    }

    public onProjectOpened(item: LeaseProject): void {
        void this.router.navigate(['lease/upload'], { queryParams: { fid: item.id } });
    }

    public onReportDownloaded(path: string): void {
        this.reportService.fetchReportAndDownload(path);
    }

    protected resetPaginationToFirstPage(): void {
        this.tableComponent.resetToFirstPage();
    }

    protected normalizeSortOption(sort: string | undefined): string | null {
        return sort === 'leases' ? 'documentCount' : sort;
    }
}
