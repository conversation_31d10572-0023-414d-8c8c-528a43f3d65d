import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { IResponseStatus } from '@core/types';
import { ItemError } from '../types/item-error.type';
import { FileMetadata } from '../types/file-metadata.type';

@Injectable()
export class FolderApi {

    constructor(private readonly http: HttpClient) {
    }

    public getFileStatus(folderId: string): Observable<IResponseStatus<FileMetadata>> {
        return this.http.get<IResponseStatus<FileMetadata>>(`/api/filed-documents/folder/${folderId}/file-status`);
    }

    public getErrors(folderId: string): Observable<ItemError[]> {
        return this.http.get<ItemError[]>(`/api/filed-documents/${folderId}/messages`);
    }

}
