import { EMPTY, Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router } from '@angular/router';
import { catchError } from 'rxjs/operators';
import { routingSchedules } from '@constants';
import { IScheduleTypeInfo } from '../types';
import { LoggerService } from '@services';
import { SchedulesService } from '../store/schedules/schedules.service';
import { ProjectsService } from '../../project-details/services/projects.service';

@Injectable()
export class SchedulesResolve implements Resolve<any> {

    constructor(
        private readonly router: Router,
        private readonly schedulesService: SchedulesService,
        private readonly log: LoggerService,
        private readonly projectsService: ProjectsService,
    ) {
    }

    public resolve(route: ActivatedRouteSnapshot): Observable<IScheduleTypeInfo[]> {
        const id = route.queryParams.fid;

        if (id) {
            return this.schedulesService
                .getSchedulesList(id)
                .pipe(
                    catchError((error: Error) => {
                        this.log.warn(error);
                        this.router.navigate([routingSchedules]);

                        return EMPTY;
                    }),
                );
        } else {
            this.projectsService.reset();
            this.router.navigate(['title']);
        }
    }
}
