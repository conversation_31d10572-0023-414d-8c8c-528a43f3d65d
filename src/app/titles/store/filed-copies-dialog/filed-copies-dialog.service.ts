import { Injectable } from '@angular/core';
import { IScheduleItem } from '../../types';
import { FiledCopiesDialogStore } from './filed-copies-dialog.store';
import { FiledCopyApi } from '../../api';
import { Observable } from 'rxjs';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';

@Injectable()
export class FiledCopiesDialogService {

    constructor(
        private readonly api: FiledCopyApi,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly filedCopiesDialogStore: FiledCopiesDialogStore,
    ) {
    }

    public selectDocument(item: IScheduleItem): void {
        this.filedCopiesDialogStore.update((state) => {
            const selectedIds = state.selectedItemIds;
            const itemId = item.id;
            const isItemSelected = selectedIds.includes(itemId);

            if (!isItemSelected) {
                return {
                    selectedItemIds: [...selectedIds, itemId],
                };
            }
        });
    }

    public unselectDocument(item: IScheduleItem): void {
        this.filedCopiesDialogStore.update((state) => {
            const selectedIds = state.selectedItemIds;
            const itemId = item.id;

            return {
                selectedItemIds: selectedIds.filter((id) => id !== itemId),
            };
        });
    }

    public deleteUploadedFile(id: string): Observable<boolean> {
        const folderId = this.projectDetailsQuery.projectId;

        return this.api.delete(folderId, id);
    }

    public getDocumentViewPath(folderId: string, documentId: string): string {
        return this.api.getDocumentPath(folderId, documentId);
    }
}
