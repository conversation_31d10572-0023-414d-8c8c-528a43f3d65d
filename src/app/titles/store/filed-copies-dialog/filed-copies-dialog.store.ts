import { Injectable } from '@angular/core';
import { Store, StoreConfig } from '@datorama/akita';
import { AvailabilityOption } from '../../enums/availability-option.enum';
import { FilterValuesList } from '../../modules/filed-copies/types/filters-list.type';

export type FiledCopiesDialogState = {
    filters: FilterValuesList;
    filterAvailability: AvailabilityOption;
    isDuplicatesGrouped: boolean;
    selectedItemIds: string[];
};

const defaultFilters = {
    filters: {},
    filterAvailability: AvailabilityOption.all,
    isDuplicatesGrouped: false,
};
const defaultState: FiledCopiesDialogState = {
    selectedItemIds: [],
    ...defaultFilters
};

@Injectable()
@StoreConfig({ name: 'filed-copies-dialog', resettable: true })
export class FiledCopiesDialogStore extends Store<FiledCopiesDialogState> {

    constructor() {
        super(defaultState);
    }

    public resetFilters(): void {
        this.update(defaultFilters);
    }
}
