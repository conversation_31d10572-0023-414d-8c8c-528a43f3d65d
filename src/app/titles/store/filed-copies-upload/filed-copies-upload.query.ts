import { Injectable } from '@angular/core';
import { QueryEntity } from '@datorama/akita';
import { FiledCopiesUploadStore, FiledCopyUploadState, UploadingScheduleItem } from './filed-copies-upload.store';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Message } from '@core/types';

@Injectable()
export class FiledCopiesUploadQuery extends QueryEntity<FiledCopyUploadState> {

    constructor(protected store: FiledCopiesUploadStore) {
        super(store);
    }

    public getByUploadId(id: string): UploadingScheduleItem | undefined {
        return this.getAll().find((filedCopy) => filedCopy.uploadId === id);
    }

    public hasPendingDocuments(): boolean {
        return this.getCount((item) => !item.isUploaded) > 0;
    }

    public selectItemErrors(): Observable<{ itemId: string; reference: string; formatedReference: string; fileId: string; messages: Message[] }[]> {
        return this.select('uploadedFiles')
            .pipe(
                map((uploadedFiles) => {
                    const uploadErrors: { itemId: string; reference: string; formatedReference: string; fileId: string; messages: Message[] }[] = [];
                    const uploadingItems = this.getAll();

                    uploadingItems.forEach((item) => {
                        const fileStatus = uploadedFiles.find((file) => file.id === item.uploadId);
                        const isError = fileStatus?.isError;
                        const isNotLinked = !fileStatus?.linkedTo;

                        if (isError || isNotLinked) {
                            const errorDetails = {
                                itemId: item.id,
                                fileId: fileStatus.id,
                                messages: fileStatus.messages,
                                reference: item.reference,
                                formatedReference: item.titleNumber,
                            };
                            uploadErrors.push(errorDetails);
                        }
                    });

                    return uploadErrors;
                }),
            );
    }

    public getItemsWithUploadTimeFails(): UploadingScheduleItem[] {
        return this.getAll().filter((item) => item.isLinkingFailed || item.isUploadFailed);
    }
}
