import { EntityState, EntityStore, StoreConfig } from '@datorama/akita';
import { IScheduleItem } from '../../types';
import { Injectable } from '@angular/core';
import { FiledCopyDocumentStatus } from '../../types/filed-copy-document-status.type';

export type UploadingScheduleItem = IScheduleItem & {
    uploadId?: string;
    isUploaded?: boolean;
    isLinkingFailed?: boolean;
    isUploadFailed?: boolean;
};

export type FiledCopyUploadState = EntityState<UploadingScheduleItem, string> & {
    uploadedFiles: FiledCopyDocumentStatus[];
    selectedToUploadFiledCopy?: IScheduleItem;
};

export const initialState = {
    uploadedFiles: [],
};


@Injectable()
@StoreConfig({ name: 'filed-copies-upload', resettable: true, idKey: 'id' })
export class FiledCopiesUploadStore extends EntityStore<FiledCopyUploadState> {
    constructor() {
        super(initialState);
    }
}
