import { Injectable } from '@angular/core';
import { FiledCopiesUploadStore, UploadingScheduleItem } from './filed-copies-upload.store';
import { Observable, Subject, Subscription } from 'rxjs';
import { FILE_TOO_LARGE, MULTIPLE_FILES_UPLOAD_DISABLED, SOMETHING_WENT_WRONG_DURING_FILES_UPLOADING } from '@constants';
import { AlertDialogComponent } from '@shared/components/dialogs/alert-dialog/alert-dialog.component';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { FiledCopyApi, ScheduleItemToLink } from '../../api';
import { FileUploader, FileUploaderEventTypes, FileUploaderService, FileValidationErrors, IFileUploaderEvents } from '../../../blocks/file-uploader';
import { ApiPolling } from '../../../core/classes/api-polling';
import { FiledCopyDocumentStatus } from '../../types/filed-copy-document-status.type';
import { IResponseStatus } from '@core/types';
import { IScheduleItem } from '../../types';
import { FiledCopiesUploadQuery } from './filed-copies-upload.query';
import { LoggerService } from '@services';
import { FiledCopiesFileUploaders } from '../../modules/filed-copies/classes/filed-copies-file-uploaders';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';

type PollResponse = IResponseStatus<FiledCopyDocumentStatus>;
type PollArgs = string;

@Injectable()
export class FiledCopiesUploadService extends FiledCopiesFileUploaders {
    public readonly fileUploaded$ = new Subject<UploadingScheduleItem>();
    private readonly uploadingPoll = new ApiPolling<PollResponse, PollArgs>(1000, 1000);
    private uploadFilesListenerSubscription: Subscription = null;
    private alertDialog: null | MatDialogRef<AlertDialogComponent>;

    constructor(
        private readonly store: FiledCopiesUploadStore,
        private readonly query: FiledCopiesUploadQuery,
        private readonly api: FiledCopyApi,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        fileUploaderService: FileUploaderService,
        dialog: MatDialog,
        log: LoggerService,
    ) {
        super(dialog, log, fileUploaderService);
        this.listenToFileUploadersEvents();
        this.uploadingPoll.setRequest((folderId) => this.api.getFolderStatus(folderId));
        this.query.select().subscribe(() => {
            this.listenForFileUpload();
        });
    }

    public openAlertDialog(errorData?: { title: string; message: string }): void {
        const isDialogOpened = this.alertDialog && !!this.alertDialog.componentInstance;
        if (!isDialogOpened) {
            this.alertDialog = this.dialog.open(AlertDialogComponent, {
                panelClass: 'report-dialog',
                width: '400px',
                data: errorData,
            });
        }
    }

    public selectFiledCopyToUpload(item: IScheduleItem): void {
        this.store.update({ selectedToUploadFiledCopy: item });
    }

    public cancelFiledCopySelection(): void {
        this.store.update({ selectedToUploadFiledCopy: null });
    }

    public clearUploadedRecords(boundedFileIds: string[]): void {
        const uploadedFiles = this.query.getAll();
        uploadedFiles.forEach((file) => {
            if (file.isUploaded && boundedFileIds.includes(file.id)) {
                this.store.remove(file.id);
            }
        });
    }

    public clearLinkingFailedItems(itemIds: string[]): void {
        const linkingFailedItems = this.query.getItemsWithUploadTimeFails();

        linkingFailedItems.forEach((item) => {
            if (itemIds.includes(item.id)) {
                this.store.remove(item.id);
                this.deleteFile(item.uploadId);
            }
        });
    }

    private listenToFileUploadersEvents(): void {
        this.fileAdded$.subscribe((event) => {
            const filesAmount = event.eventInstance;
            const fileUploader = this.getFileUploader(event.uploaderKey);

            this.onReadyToUpload(filesAmount, fileUploader);
        });
        this.events$.subscribe((event) => {
            const eventType = event.eventInstance;
            const fileUploader = this.getFileUploader(event.uploaderKey);

            switch (event.eventInstance.type) {
                case FileUploaderEventTypes.fileUploadingStarted:
                    this.onFilePreUploadStarted(eventType);
                    break;
                case FileUploaderEventTypes.fileUploadingCanceled:
                    this.onFileUploadingCanceled(eventType);
                    break;
                case FileUploaderEventTypes.fileUploadingSucceeded:
                    this.onFileUploadingSucceeded(fileUploader, eventType);
                    break;
                case FileUploaderEventTypes.fileUploadingFailed:
                    this.onFileUploadingFailed(fileUploader, eventType);
                    break;
            }
        });
        this.errors$.subscribe((error) => {
            this.log.warn('File upload error', error);

            if (error.eventInstance?.errorType === FileValidationErrors.validationMaxFileSizeExceeded) {
                this.openAlertDialog(FILE_TOO_LARGE);
            }
        });
    }

    private onReadyToUpload(amount: number, fileUploader: FileUploader): void {
        if (!amount) {
            return;
        }

        const isMultipleUploadAllowed = fileUploader.isMultipleFilesAllowed();
        if (!isMultipleUploadAllowed && amount > 1) {
            this.openAlertDialog(MULTIPLE_FILES_UPLOAD_DISABLED);
            fileUploader.removeAll();

            return;
        }

        this.startFilesUploading(fileUploader);
    }

    private startFilesUploading(fileUploader: FileUploader): void {
        const filedCopy = this.store.getValue().selectedToUploadFiledCopy;
        const isFileExist = !!filedCopy?.file;
        const uploadingFiles = this.store.getValue().ids;
        const isUploadingInProgress = uploadingFiles.includes(filedCopy?.id);

        if (filedCopy && !isFileExist && !isUploadingInProgress) {
            const folderId = this.projectDetailsQuery.projectId;
            const documentType = filedCopy.documentType;
            const url = this.api.createUploadLink(folderId, documentType);

            fileUploader.updateUploaderUrl(url);
            fileUploader.uploadAll();
        } else {
            fileUploader.removeAll();
        }
    }

    private onFilePreUploadStarted(event: IFileUploaderEvents): void {
        const selectedItem = this.query.getValue().selectedToUploadFiledCopy;
        const temporaryFileId = event.file.uuid;
        const filedCopyWithTempId = {
            ...selectedItem,
            uploadId: temporaryFileId,
        };

        this.store.upsert(selectedItem.id, filedCopyWithTempId);
        this.cancelFiledCopySelection();
    }

    private onFileUploadingFailed(fileUploader: FileUploader, event?: IFileUploaderEvents): void {
        this.log.warn('File upload error', event);

        const error = event ? event.error : Error();
        const temporaryFileId = event ? event.file.uuid : '';
        const errorData = { ...SOMETHING_WENT_WRONG_DURING_FILES_UPLOADING };

        if (error.status === 413) {
            const fileName = event.file.fileAsObject.name || 'File';
            errorData.title = 'Too Large File';
            errorData.message = `${fileName} is too large to be a valid document.`;
        }

        this.openAlertDialog(errorData);
        this.cancelPreUploading(temporaryFileId);
        fileUploader.removeAll();
    }

    private onFileUploadingCanceled(event: IFileUploaderEvents): void {
        const temporaryFileId = event.file.uuid;
        this.cancelPreUploading(temporaryFileId);
    }

    private cancelPreUploading(documentId: string): void {
        this.removeUploadingFiledCopy(documentId);
        this.store.update({ selectedToUploadFiledCopy: null });
    }

    private onFileUploadingSucceeded(fileUploader: FileUploader, event: IFileUploaderEvents): void {
        const temporaryFileId = event.file.uuid;
        const fileId = event.response.body[0];
        const folderId = this.projectDetailsQuery.projectId;

        fileUploader.removeFile(event.file);
        this.link(temporaryFileId, fileId);
        this.uploadingPoll.start((status) => this.updateDocuments(status.documents), folderId);
    }

    private link(uploadId: string, realId: string): void {
        const filedCopy = this.query.getByUploadId(uploadId);

        if (filedCopy) {
            this.store.update(filedCopy.id, { uploadId: realId });
            this.linkUploadedDocumentToFiledCopy(realId, filedCopy)
                .subscribe({
                    error: () => {
                        this.store.update(filedCopy.id, { isLinkingFailed: true });
                    },
                    next: () => {
                        this.store.update(filedCopy.id, { isLinkingFailed: false });
                    },
                });
        }
    }

    private removeUploadingFiledCopy(uploadId: string): void {
        const uploadingFiledCopy = this.query.getByUploadId(uploadId);
        this.store.remove(uploadingFiledCopy.id);
    }

    private listenForFileUpload(): void {
        if (this.uploadFilesListenerSubscription) {
            return;
        }

        this.uploadFilesListenerSubscription = this.query.select('uploadedFiles')
            .subscribe((uploadedDocuments) => {
                uploadedDocuments.forEach((document) => {
                    const uploadingDocument = this.query.getByUploadId(document.id);
                    const isDocumentUploaded = document.isAccepted && !document.isError;

                    if (!uploadingDocument || uploadingDocument.isUploaded) {
                        return;
                    }

                    if (isDocumentUploaded) {
                        this.fileUploaded$.next(uploadingDocument);
                        this.store.update(uploadingDocument.id, { isUploaded: true });
                    } else if (document.isError) {
                        this.store.update(uploadingDocument.id, { isUploadFailed: true });
                    }
                });

                const isUploadQueueEmpty = !this.query.hasPendingDocuments();
                if (isUploadQueueEmpty) {
                    this.stopUploadingPoll();
                }
            });
    }

    private stopUploadingPoll(): void {
        this.uploadingPoll.stop();

        if (this.uploadFilesListenerSubscription) {
            this.uploadFilesListenerSubscription.unsubscribe();
            this.uploadFilesListenerSubscription = null;
        }
    }

    private updateDocuments(documents: FiledCopyDocumentStatus[]): void {
        this.store.update({ uploadedFiles: documents });
    }

    private linkUploadedDocumentToFiledCopy(uploadedDocumentId: string, filedCopy: IScheduleItem): Observable<void> {
        const folderId = this.projectDetailsQuery.projectId;
        const filedCopyToLink: ScheduleItemToLink = {
            documentId: filedCopy.documentId,
            kind: filedCopy.kind,
            reference: filedCopy.reference,
            filedUnder: filedCopy.filedUnder,
            documentDate: filedCopy.documentDate,
            entryNumbers: filedCopy.entryNumbers,
            documentTypeCode: filedCopy.documentTypeCode,
        };

        return this.api.linkDocument(folderId, uploadedDocumentId, filedCopyToLink);
    }

    private deleteFile(id: string): void {
        const folderId = this.projectDetailsQuery.projectId;
        this.api.delete(folderId, id).subscribe();
    }
}
