import { Query } from '@datorama/akita';
import { Injectable } from '@angular/core';
import { LandRegistryDialogState, LandRegistryDialogStore } from './land-registry-dialog.store';
import { TitleBasketItem } from '../../types';
import { Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';

@Injectable()
export class LandRegistryDialogQuery extends Query<LandRegistryDialogState> {
    constructor(
        protected store: LandRegistryDialogStore,
    ) {
        super(store);
    }

    public getTitlesFromBasket$(): Observable<TitleBasketItem[]> {
        return this.select('basketTitles');
    }

    public getTitlesFromBasket(): TitleBasketItem[] {
        return this.getValue().basketTitles;
    }

    public countTitlesInBasket$(): Observable<number> {
        return this.getTitlesFromBasket$()
            .pipe(switchMap((titlesList) => of(titlesList.length)));
    }

    public totalPriceOfTitlesBasket$(): Observable<number> {
        return this.getTitlesFromBasket$()
            .pipe(
                switchMap((titlesList) =>
                    of(titlesList.reduce((acc, value) => acc + value.cost, 0)),
                ),
            );
    }

    public totalPriceOfTitlesBasket(): number {
        const titlesList = this.store.getValue().basketTitles;

        return titlesList.reduce((acc, value) => acc + value.cost, 0);
    }

    public isTitleAddedToBasket$(titleNumber: string): Observable<boolean> {
        return this.select()
            .pipe(
                switchMap((state) => {
                    const titles = state.basketTitles;
                    const isTileExist = titles.some((el) => el.titleNumber === titleNumber);

                    return of(isTileExist);
                }),
            );
    }

    public selectBasketTitles(): Observable<TitleBasketItem[]> {
        return this.select('basketTitles');
    }

    public selectIsAnyTitlePending(): Observable<boolean> {
        return this.select('basketPendingTitles')
            .pipe(
                map((titles) => !!titles.length),
            );
    }

    public getIsTitlePending(titleNumber: string): boolean {
        return this.getValue().basketPendingTitles.includes(titleNumber);
    }

    public getIsTitleInBasket(titleNumber: string): boolean {
        return this.getValue().basketTitles.some((title) => title.titleNumber === titleNumber);
    }
}
