import { Store, StoreConfig } from '@datorama/akita';
import { Injectable } from '@angular/core';
import { TitleBasketItem } from '../../types';
import { SearchRegistry } from '../../enums/search-register.enum';

export enum PurchasingStatus {
    success = 'success',
    failure = 'error'
}

export type LandRegistryDialogState = {
    registry: SearchRegistry;
    basketTitles: TitleBasketItem[];
    basketPendingTitles: string[];
}

function createInitialState(): LandRegistryDialogState {
    return {
        registry: SearchRegistry.hmlr,
        basketTitles: [],
        basketPendingTitles: [],
    };
}

@Injectable()
@StoreConfig({ name: 'land-registry-dialog' })
export class LandRegistryDialogStore extends Store<LandRegistryDialogState> {

    constructor() {
        super(createInitialState());
        this.setLoading(false);
    }

}
