import { Injectable } from '@angular/core';
import { Store, StoreConfig } from '@datorama/akita';
import { ProjectMetrics } from '@core/types';

export type NotifyCountersState = {
    purchased: ProjectMetrics;
    bookmarks: ProjectMetrics;
    teamProjects: ProjectMetrics;
}

@Injectable()
@StoreConfig({ name: 'notify-counters' })
export class NotifyCountersStore extends Store<NotifyCountersState> {
    constructor() {
        super({});
    }
}

