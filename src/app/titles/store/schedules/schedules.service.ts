import { Injectable } from '@angular/core';
import { EMPTY, forkJoin, interval, Observable, of, Subject, throwError, timer } from 'rxjs';
import { catchError, concatMap, delay, filter, finalize, first, map, switchMap, take, takeWhile, tap } from 'rxjs/operators';
import { IPurchasedFileBase, ISchedule, IScheduleItem, ISchedulePurchaseParams, IScheduleType, IScheduleTypeInfo, ScheduleKey } from '../../types';
import { SchedulesApi } from '../../api';
import { FiledCopiesDialogQuery, ScheduleFilesUploadService, SchedulesQuery, SchedulesStore, SearchFilesService } from '../index';
import { LoggerService, ProfileService } from '@services';
import { searchSchedules } from '../../constants/client-side-schdules.constant';
import { HttpStatusesHandlerService } from '../../services';
import { OnboardingManageService } from '../../../onboarding/services';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';

@Injectable()
export class SchedulesService {
    public purchaseFinishEvent$ = new Subject<ISchedule>();

    constructor(
        private readonly schedulesApi: SchedulesApi,
        private readonly schedulesStore: SchedulesStore,
        private readonly schedulesQuery: SchedulesQuery,
        private readonly profileService: ProfileService,
        private readonly scheduleFilesUploadService: ScheduleFilesUploadService,
        private readonly searchFilesService: SearchFilesService,
        private readonly httpStatusesHandlerService: HttpStatusesHandlerService,
        private readonly onboardingService: OnboardingManageService,
        private readonly filedCopiesQuery: FiledCopiesDialogQuery,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly log: LoggerService,
        private readonly filedCopiesDialogQuery: FiledCopiesDialogQuery,
    ) {
    }

    public getSchedulesList(folderId: string): Observable<IScheduleTypeInfo[]> {
        this.schedulesStore.setLoading(true);

        return this.schedulesApi.getSchedulesTypeList(folderId)
            .pipe(
                switchMap((types: IScheduleType[]) => {
                    this.log.info('getSchedulesTypeList.switchMap', types);
                    const requests = [];

                    types.forEach((scheduleType: IScheduleType) => {
                        this.log.info('getScheduleTypeInfo', scheduleType.url);
                        requests.push(
                            this.schedulesApi.getScheduleTypeInfo(scheduleType.url)
                                .pipe(
                                    map((info) => ({ ...scheduleType, ...info })),
                                    catchError((error) => {
                                        this.log.error(`Failed to fetch schedule type info for ${scheduleType.key}:`, error);

                                        return of({ ...scheduleType, isError: true });
                                    }),
                                ),
                        );
                    });

                    return requests.length ? forkJoin(requests) : [];
                }),
                catchError((error) => {
                    this.schedulesStore.setLoading(false);
                    this.schedulesStore.set([]);

                    return throwError(error);
                }),
            )
            .pipe(
                tap((schedules: ISchedule[]) => {
                    this.log.info('Schedules list:', schedules);
                    this.schedulesStore.setLoading(false);

                    const filteredSchedules = schedules.filter((schedule) => !schedule.isError);

                    this.initUploadSchedules();
                    this.schedulesStore.add(filteredSchedules);
                }),
                catchError((error) => {
                    this.schedulesStore.setLoading(false);
                    this.schedulesStore.set([]);

                    return of(error);
                }),
            );
    }

    public initUploadSchedules(): void {
        const isSearchSchedulesEnabled = this.profileService.isSearchesEnabled$.getValue();
        const schedulesWithSearches = isSearchSchedulesEnabled ? [...searchSchedules] : [];

        this.schedulesStore.upsertMany(schedulesWithSearches);
        this.scheduleFilesUploadService.initSchedules(schedulesWithSearches);
    }

    public fetchSchedules(folderId: string, scheduleKeys: ScheduleKey[]): Observable<IScheduleTypeInfo[]> {
        this.schedulesStore.setLoading(true);

        return this.schedulesApi.getSchedulesTypeList(folderId)
            .pipe(
                switchMap((types: IScheduleType[]) => {
                    const requests = [];

                    types.forEach((scheduleType: IScheduleType) => {
                        if (scheduleKeys.includes(scheduleType.key)) {
                            requests.push(
                                this.schedulesApi.getScheduleTypeInfo(scheduleType.url)
                                    .pipe(
                                        map((info) => ({ ...scheduleType, ...info })),
                                    ),
                            );
                        }
                    });

                    return requests.length ? forkJoin(requests) : [];
                }),
                catchError((error) => {
                    return of(throwError(error));
                }),
                tap((schedules: ISchedule[]) => {
                    schedules.forEach((schedule: ISchedule) => {
                        if (scheduleKeys.includes(schedule.key)) {
                            this.schedulesStore.upsert(schedule.key, { ...schedule });
                        }
                    });
                }),
                finalize(() => this.schedulesStore.setLoading(false)),
            );
    }

    public startSchedulePurchase(
        schedule: ISchedule,
        folderId: string,
        allSchedule: ISchedule,
        selectedItems: IScheduleItem[],
    ): Observable<void> {
        const key = schedule.key;
        const initialProgress = 9;

        this.schedulesStore.update(key, { realProgress: initialProgress });

        if (key === ScheduleKey.linkedDocuments) {
            schedule = JSON.parse(JSON.stringify(schedule));
            schedule.items = selectedItems.length
                ? selectedItems
                : schedule.items.filter((item) => !!item.isAvailable && !!item.offerPurchase);
        }

        const params: ISchedulePurchaseParams[] = (schedule.items || [])
            .map((item) => {
                const id = item.id;
                const reference = item.reference || item.titleNumber;
                const documentType = item.documentType;
                const documentTypeCode = item.documentTypeCode;
                const documentDate = item.documentDate;
                const entryNumbers = item.entryNumbers;
                const filedUnder = item.filedUnder;
                const linkedDocuments = {
                    ...(documentType && { documentType }),
                    ...(documentTypeCode && { documentTypeCode }),
                    ...(documentDate && { documentDate }),
                    ...(entryNumbers && { entryNumbers }),
                    ...(filedUnder && { filedUnder }),
                };

                return {
                    kind: key,
                    ...(reference && { reference }),
                    ...(id && { id }),
                    ...linkedDocuments,
                };
            });

        this.schedulesApi.startSchedulePurchase(folderId, key, params)
            .pipe(
                map((response) => response.headers.get('Content-Location')),
                concatMap((url) => this.getSchedulePurchaseStatus(url, schedule, allSchedule)),
                catchError((error) => {
                    this.schedulesStore.update(key, { realProgress: 100, isError: true });
                    this.httpStatusesHandlerService.handleSchedulesCode(error);

                    if (error?.status === 423) {
                        return EMPTY;
                    }

                    return throwError(error);
                }),
            )
            .subscribe();

        return this.animatePurchasingProgressBar(schedule);
    }

    public startSearchDocumentsProcessing(schedule: ISchedule): void {
        this.searchFilesService.startProcessing(schedule);
    }

    public fetchDocuments(folderId: string): Observable<IScheduleTypeInfo[]> {
        return this.fetch(folderId, [ScheduleKey.linkedDocuments]);
    }

    public fetchDocumentsAndAnalysis(folderId: string): Observable<IScheduleTypeInfo[]> {
        return this.fetch(folderId, [ScheduleKey.linkedDocuments, ScheduleKey.linkedDocumentsAnalysis]);
    }

    public recalculatePrice(): void {
        const schedule = this.schedulesQuery.getEntity(ScheduleKey.linkedDocuments);

        if (!schedule?.items.length) {
            return;
        }

        const items = schedule.items;
        const selectedItemIds = this.filedCopiesDialogQuery.getValue().selectedItemIds;
        const availableSelectedItems = items.filter((item) => item.isAvailable && item.offerPurchase && selectedItemIds.includes(item.id));
        const digitalRegex = /^(\D*)(.*)/;
        const unitCost = parseFloat(digitalRegex.exec(schedule.unitCost)[2]);
        const costCurrency = digitalRegex.exec(schedule.unitCost)[1];
        const availableSelectedItemsCost = unitCost * availableSelectedItems.length;
        const totalCost = costCurrency + parseFloat(String(availableSelectedItemsCost)).toFixed(2);

        this.schedulesStore.upsert(schedule.key, { totalCost });
    }

    public fetch(folderId: string, scheduleKeys: ScheduleKey[]): Observable<IScheduleTypeInfo[]> {
        return this.fetchSchedules(folderId, scheduleKeys)
            .pipe(
                catchError((error) => {
                    this.httpStatusesHandlerService.handleSchedulesCode(error);

                    return throwError(error);
                }),
                tap(() => {
                    this.recalculatePrice();
                }),
            );
    }

    public markDocumentsAsPurchased(key: ScheduleKey, itemIds: string[]): void {
        this.schedulesStore.update(key, (schedule) => {
            return {
                ...schedule,
                items: schedule.items.map((item) =>
                    itemIds.includes(item.id) ? { ...item, offerPurchase: false, isPurchased: true } : item
                ),
            };
        });
    }

    public purchase(schedule: ISchedule): void {
        const folderId = this.projectDetailsQuery.projectId;
        const selectedItems = this.filedCopiesQuery.getSelectedAvailableToSelectItems();

        this.incrementActionsInProgress();
        this.startSchedulePurchase(schedule, folderId, schedule, selectedItems)
            .pipe(
                finalize(() => this.decrementActionsInProgress()),
                catchError((error) => throwError(error)),
            )
            .subscribe();
    }

    public purchaseLinkedDocument(schedule: ISchedule, folderId: string, selectedItems: IScheduleItem[]): Observable<IPurchasedFileBase[]> {
        if (schedule.key !== 'linked-documents') {
            return of([]);
        }

        schedule = JSON.parse(JSON.stringify(schedule));
        schedule.items = selectedItems;

        const kind = ScheduleKey.linkedDocuments;
        const params: ISchedulePurchaseParams[] = (schedule.items || [])
            .map((item) => {
                const id = item.id;
                const reference = item.reference || item.titleNumber;
                const documentType = item.documentType;
                const documentTypeCode = item.documentTypeCode;
                const documentDate = item.documentDate;
                const entryNumbers = item.entryNumbers;
                const filedUnder = item.filedUnder;

                return {
                    kind,
                    ...(id && { id }),
                    ...(reference && { reference }),
                    ...(documentType && { documentType }),
                    ...(documentTypeCode && { documentTypeCode }),
                    ...(documentDate && { documentDate }),
                    ...(entryNumbers && { entryNumbers }),
                    ...(filedUnder && { filedUnder }),
                };
            });

        return this.schedulesApi.startSchedulePurchase(folderId, kind, params)
            .pipe(
                map((response) => response.headers.get('Content-Location')),
                concatMap((url) => this.getSchedulePurchaseStatusSimple(url)),
                catchError((error) => {
                    this.httpStatusesHandlerService.handleSchedulesCode(error);

                    if (error?.status === 423) {
                        return EMPTY;
                    }

                    return throwError(error);
                }),
            );
    }

    public fetchLinkedDocumentItems(folderId: string): void {
        this.incrementActionsInProgress();
        this.fetchDocuments(folderId)
            .pipe(
                finalize(() => this.decrementActionsInProgress()),
                catchError((error) => {
                    return throwError(error);
                }),
            )
            .subscribe();
    }

    private onPurchaseFinish(schedule: ISchedule): void {
        const folderId = this.projectDetailsQuery.projectId;
        this.schedulesStore.update(schedule.key, { isComplete: true });

        if (this.onboardingService.isActive) {
            this.onboardingService.closeActiveOverlay();
        }

        if (schedule.key === ScheduleKey.linkedDocumentsAnalysis || schedule.key === ScheduleKey.linkedDocuments) {
            this.incrementActionsInProgress();
            this.fetchDocumentsAndAnalysis(folderId)
                .pipe(
                    finalize(() => {
                        this.decrementActionsInProgress();
                        this.purchaseFinishEvent$.next(schedule);
                    }),
                )
                .subscribe();
        } else {
            this.purchaseFinishEvent$.next(schedule);
        }
    }

    private getSchedulePurchaseStatusSimple(url: string): Observable<IPurchasedFileBase[]> {
        return timer(1, 1500)
            .pipe(
                concatMap(() => this.schedulesApi.getSchedulePurchaseStatus(url)),
                takeWhile((response) => response.status < 400),
                filter((response) => response.status === 200),
                first(),
                catchError((error) => {
                    this.httpStatusesHandlerService.handleSchedulesCode(error);

                    return throwError(error);
                }),
                map((response) => (response.body || [])),
            );
    }

    private getSchedulePurchaseStatus(url: string, schedule: ISchedule, allSchedule: ISchedule): Observable<void> {
        const key = schedule.key;

        return timer(1, 3000)
            .pipe(
                concatMap(() => this.schedulesApi.getSchedulePurchaseStatus(url)),
                tap((response) => {
                    const progress = response.headers.get('progress%');
                    const percent = parseFloat(progress || (response.status === 200 ? '100' : '10'));
                    this.schedulesStore.update(key, { realProgress: percent });
                }),
                filter((response) => response.status === 200 && !!response.body),
                map((response) => {
                    const errors = (response.body || []).filter((schedule) => schedule.isError);
                    const errLength = errors.length;

                    if (key === ScheduleKey.linkedDocuments) {
                        const selectedItems = schedule.items;
                        const allItems = allSchedule.items.map((item) => ({ ...item }));

                        if (allItems.length > 0) {
                            allItems.forEach((_, i) => {
                                if (selectedItems.length > 0) {
                                    selectedItems.forEach((item) => {
                                        if (item.id === allItems[i].id) {
                                            allItems[i].offerPurchase = false;
                                        }
                                    });
                                }
                            });
                        }

                        this.schedulesStore.upsert(key, {
                            ...allSchedule,
                            items: allItems,
                            errors,
                            ...(((schedule.items || []).length === errLength && (!(schedule.isUpdated && (schedule.items || []).length === 0))) && { isEmpty: true }),
                        });
                    } else {
                        this.schedulesStore.upsert(key, {
                            ...schedule,
                            allPurchased: true,
                            errors,
                            ...(((schedule.items || []).length === errLength && (!(schedule.isUpdated && (schedule.items || []).length === 0))) && { isEmpty: true }),
                            isError: true,
                        });
                    }
                }),
                first(),
                catchError((error) => {
                    this.schedulesStore.update(key, { realProgress: 100, isError: true });
                    this.httpStatusesHandlerService.handleSchedulesCode(error);

                    return throwError(error);
                }),
            );
    }

    private incrementActionsInProgress(): void {
        this.schedulesStore.update((state) => ({ actionsInProgressAmount: state.actionsInProgressAmount + 1 }));
    }

    private decrementActionsInProgress(): void {
        this.schedulesStore.update((state) => ({ actionsInProgressAmount: state.actionsInProgressAmount - 1 }));
    }

    private animatePurchasingProgressBar(schedule: ISchedule): Observable<void> {
        const key = schedule.key;

        this.schedulesStore.update(key, { displayedProgress: 0, isComplete: false });

        return interval(1)
            .pipe(
                map((timer) => {
                    const schedule = this.schedulesQuery.getEntity(key);

                    if (!schedule) {
                        return 100;
                    }

                    const realProgress = schedule.realProgress;
                    const displayedProgress = schedule.displayedProgress;
                    const speed = displayedProgress <= 10 && realProgress <= 20
                        ? 100
                        : (realProgress === 100 ? 5 : 10);

                    if (timer % speed === 0) {
                        const nextProgressValue = Math.min(displayedProgress + 1, realProgress);

                        if (nextProgressValue !== displayedProgress) {
                            this.schedulesStore.update(key, { displayedProgress: nextProgressValue });
                        }

                        return nextProgressValue;
                    }

                    return displayedProgress;
                }),
                filter((value) => value >= 100),
                take(1),
                delay(1000),
                finalize(() => {
                    this.onPurchaseFinish(schedule);
                }),
                map(() => void 0),
            );
    }
}
