import { Injectable } from '@angular/core';
import { SearchFileSortBy, SearchFilesStore } from './search-files.store';
import { BehaviorSubject, EMPTY, Observable, of, Subject } from 'rxjs';
import { catchError, delay, filter, finalize, first, map, repeat, switchMap, takeUntil, tap } from 'rxjs/operators';
import { SOMETHING_WENT_GONE_REFRESH_PAGE } from '@constants';
import { AlertDialogComponent } from '@shared/components/dialogs/alert-dialog/alert-dialog.component';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { FilesUploadedSnackbarComponent } from '@shared/components/files-uploaded-snackbar/files-uploaded-snackbar.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SearchFilesFolderStatus } from '../../types/search-files-folder-status.type';
import { createEmptySearchFile, SearchFile } from '../../types/search-file.type';
import { SearchFilesQuery } from './search-files.query';
import { SearchFilesApi } from '../../api/search-files.api';
import { Order } from '@datorama/akita';
import { SearchType } from '../../enums';
import { ISchedule } from '../../types';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';

@Injectable()
export class SearchFilesService {
    private readonly fileMetadataPollingDelayMs = 3000;
    private readonly folderStatus$ = new BehaviorSubject<SearchFilesFolderStatus>(null);
    private readonly pollingActive$ = new Subject<boolean>();

    private folderStatusPoll: Observable<SearchFilesFolderStatus> | null;
    private alertDialog: null | MatDialogRef<AlertDialogComponent>;

    constructor(
        private readonly store: SearchFilesStore,
        private readonly query: SearchFilesQuery,
        private readonly api: SearchFilesApi,
        private readonly snackBar: MatSnackBar,
        private readonly dialog: MatDialog,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
    ) {
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    public startProcessing(schedule: ISchedule): void {
        // TODO: add implementation
    }

    public startUploading(): void {
        this.store.setLoading(true);
    }

    public loadSearchFiles(searchesType?: SearchType): void {
        const folderId = this.projectDetailsQuery.projectId;
        this.api.getFolderStatus(folderId)
            .pipe(
                tap(() => this.store.setLoading(true)),
                finalize(() => this.store.setLoading(false)),
            )
            .subscribe((folderStatus) => {
                const files = folderStatus.documents;
                const filteredFiles = searchesType
                    ? this.filterSearchFilesByType(files, searchesType)
                    : files;

                this.store.set(filteredFiles);
            });
    }

    public filterSearchFilesByType(files: SearchFile[], searchesType: SearchType): SearchFile[] {
        if (searchesType === SearchType.other) {
            return files.filter((file) => file.type !== SearchType.local);
        }

        return files.filter((file) => file.type === searchesType);
    }

    public linkTitles(fileId: string, titleNumbers: string[]): void {
        const folderId = this.projectDetailsQuery.projectId;
        this.api.assignTitlesToDocument(folderId, fileId, titleNumbers)
            .subscribe(() => {
                this.store.update(fileId, { linkedTitles: titleNumbers });
            });
    }

    public setSortParams(sortBy: SearchFileSortBy, sortByOrder: Order): void {
        this.store.update(() => ({ sortBy, sortByOrder }));
    }

    public removeErrorFileMessages(fileId: string): void {
        this.store.update((state) => {
            const filteredErrors = state.errors.filter((error) => error.fileId !== fileId);

            return { errors: filteredErrors };
        });
    }

    public setType(fileId: string, fileType: string): void {
        const folderId = this.projectDetailsQuery.projectId;
        this.api.updateDocumentType(folderId, fileId, fileType)
            .subscribe(() => {
                this.store.update(fileId, { type: fileType });
            });
    }

    public removeOrCancelFile(fileId: string): void {
        const isFileExists = this.query.hasEntity(fileId);
        if (!isFileExists) {
            return;
        }

        const isTemporaryFile = this.query.isTemporaryFile(fileId);
        if (!isTemporaryFile) {
            this.removeFileFromBackendSide(fileId);
        }

        this.store.remove(fileId);

        const isAllFileAccepted = this.query.isAllFileAccepted();
        if (isAllFileAccepted) {
            this.store.setLoading(false);
        }
    }

    public addTemporaryFile(temporaryFileId: string, searchType?: SearchType): void {
        const emptyFile = createEmptySearchFile(temporaryFileId);
        emptyFile.type = searchType ?? '';

        this.store.add(emptyFile);
    }

    public saveUploadedFile(temporaryFileId: string, fileId: string): void {
        this.folderStatus$
            .pipe(
                filter((folderStatus) => !!folderStatus),
                map((folderStatus) => folderStatus.documents.find((file) => file.id === fileId)),
                filter((newFile) => !!newFile && newFile.isProcessed),
                first(),
                finalize(() => {
                    this.store.remove(temporaryFileId);
                }),
            )
            .subscribe((file) => {
                this.store.add(file, { loading: true });
            });

        this.startFolderStatusPoll();
    }

    public stopFolderStatusPoll(): void {
        this.pollingActive$.next(false);
    }

    public showUploadedSnackbar(filesAmount: number): void {
        this.snackBar.openFromComponent(FilesUploadedSnackbarComponent,
            {
                data: {
                    filesAmount,
                    messageForOneFile: '1 search document',
                    messageForManyFiles: `${filesAmount} search documents`,
                },
                panelClass: ['files-uploaded-snackbar', 'up-125'],
                duration: 6000,
            },
        );
    }

    public openAlertDialog(errorData?: { title: string; message: string }): void {
        const isDialogOpened = this.alertDialog && !!this.alertDialog.componentInstance;
        if (!isDialogOpened) {
            this.alertDialog = this.dialog.open(AlertDialogComponent, {
                panelClass: 'report-dialog',
                width: '400px',
                data: errorData,
            });
        }
    }

    public clearLoadedSearches(): void {
        this.store.reset();
    }

    private startFolderStatusPoll(): void {
        if (!this.folderStatusPoll) {
            this.folderStatusPoll = of({})
                .pipe(
                    map(() => this.projectDetailsQuery.projectId),
                    filter((folderId) => !!folderId),
                    switchMap((folderId) => this.api.getFolderStatus(folderId)),
                    tap((status) => {
                        this.folderStatus$.next(status);
                        this.checkUploadFinish();
                    }),
                    delay(this.fileMetadataPollingDelayMs),
                    repeat(),
                    takeUntil(this.pollingActive$),
                    catchError(() => {
                        this.openAlertDialog(SOMETHING_WENT_GONE_REFRESH_PAGE);

                        return EMPTY;
                    }),
                    finalize(() => this.folderStatusPoll = null),
                );
            this.folderStatusPoll.subscribe();
        }
    }

    private checkUploadFinish(): void {
        const isAllFileAccepted = this.query.isAllFileAccepted();

        if (isAllFileAccepted) {
            const uploadedFilesAmount = this.query.getCount();

            if (uploadedFilesAmount) {
                this.showUploadedSnackbar(uploadedFilesAmount);
            }

            this.store.setLoading(false);
            this.stopFolderStatusPoll();
        } else {
            this.startFolderStatusPoll();
        }
    }

    private removeFileFromBackendSide(fileId: string): void {
        const folderId = this.projectDetailsQuery.projectId;
        this.api.deleteDocumentFromFolder(folderId, fileId).subscribe();
    }
}
