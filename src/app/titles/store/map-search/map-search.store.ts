import { Injectable } from '@angular/core';
import { Store, StoreConfig } from '@datorama/akita';

import { ITitleInfo, ShortTitleInfo } from '../../types';
import { GeoJSONFeature, LngLat } from 'maplibre-gl';
import { GeoJsonFeatureMap, MapBounds } from '../../modules/map-search/types/mapping-bounds.type';
import { MapFilters } from '../../modules/map-search/types/map-filter-options.type';

export type MapSearchState = MapFilters & {
    isSidebarVisible: boolean;
    isFiltersVisible: boolean;
    markerPosition?: LngLat;
    featuresMap?: GeoJsonFeatureMap;
    sidePanelTitles: ShortTitleInfo[];
    selectedFeatures: GeoJSONFeature[];
    temporaryHighlightedTitleNumber?: string;
    permanentlyHighlightedTitleNumber?: string;
    searchedTitleNumber?: string;
    loadedBounds?: MapBounds;
    details?: ITitleInfo;
    focusedFeatures?: GeoJSONFeature[];
    isSelectionUnderPinBlocked: boolean;
    stateBeforeFocusing?: { zoom: number; center: LngLat };
    previousShowTitleId: string;
    isSidePanelLoading: boolean;
    isMapComponentInitialized: boolean;
    fetchRequestsInProgress: number;
}

function createInitialState(): MapSearchState {
    return {
        isSidebarVisible: false,
        isFiltersVisible: false,
        isFreeholdsOn: true,
        isLeaseholdsOn: true,
        isNATenureOn: true,
        details: null,
        previousShowTitleId: null,
        temporaryHighlightedTitleNumber: null,
        permanentlyHighlightedTitleNumber: null,
        searchedTitleNumber: null,
        sidePanelTitles: [],
        selectedFeatures: [],
        isSidePanelLoading: false,
        isMapComponentInitialized: false,
        isSelectionUnderPinBlocked: false,
        fetchRequestsInProgress: 0,
    };
}

@Injectable()
@StoreConfig({ name: 'map-search', resettable: true })
export class MapSearchStore extends Store<MapSearchState> {

    constructor() {
        super(createInitialState());
    }

}
