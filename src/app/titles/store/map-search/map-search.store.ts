import { Injectable } from '@angular/core';
import { Store, StoreConfig } from '@datorama/akita';

import { ITitleInfo, ShortTitleInfo } from '../../types';
import { Observable, Subject } from 'rxjs';
import { GeoJSONFeature, LngLat } from 'maplibre-gl';
import { GeoJsonFeatureMap, MapBounds } from '../../modules/map-search/types/mapping-bounds.type';
import { ProfileService } from '@services';

export type MapSearchState = {
    isSidebarVisible: boolean;
    isFiltersVisible: boolean;
    zoom: number;
    center?: LngLat;
    markerPosition?: LngLat;
    isFreeholdsOn: boolean;
    isLeaseholdsOn: boolean;
    featuresMap?: GeoJsonFeatureMap;
    sidePanelTitles: ShortTitleInfo[];
    selectedFeatures: GeoJSONFeature[];
    temporaryHighlightedTitleNumber?: string;
    permanentlyHighlightedTitleNumber?: string;
    searchedTitleNumber?: string;
    loadedBounds?: MapBounds;
    details?: ITitleInfo;
    focusedFeatures?: GeoJSONFeature[];
    isSelectionUnderPinBlocked: boolean;
    stateBeforeFocusing?: { zoom: number; center: LngLat };
    previousShowTitleId: string;
    isSidePanelLoading: boolean;
    isMapComponentInitialized: boolean;
}

function createInitialState(): MapSearchState {
    return {
        isSidebarVisible: false,
        isFiltersVisible: false,
        zoom: 5,
        center: new LngLat(-3.5273437499999893, 54.71905799874775),
        isFreeholdsOn: true,
        isLeaseholdsOn: true,
        details: null,
        previousShowTitleId: null,
        temporaryHighlightedTitleNumber: null,
        permanentlyHighlightedTitleNumber: null,
        searchedTitleNumber: null,
        sidePanelTitles: [],
        selectedFeatures: [],
        isSidePanelLoading: false,
        isMapComponentInitialized: false,
        isSelectionUnderPinBlocked: false,
    };
}

@Injectable()
@StoreConfig({ name: 'map-search', resettable: true })
export class MapSearchStore extends Store<MapSearchState> {
    private readonly locationChange = new Subject<{ location: LngLat; zoom?: number }>();

    constructor(
        private readonly profile: ProfileService,
    ) {
        super(createInitialState());
        this.updateFiltersStateDependOnZoom();
    }

    public navigateTo(point: LngLat, zoom?: number): void {
        this.locationChange.next({ location: point, zoom });
    }

    public getLocationChanged(): Observable<{ location: LngLat; zoom?: number }> {
        return this.locationChange.asObservable();
    }

    private updateFiltersStateDependOnZoom(): void {
        const zoom = this.getValue().zoom;
        const isFiltersVisible = zoom >= this.profile.mapSearchMinZoomFeatureVisibility$.getValue();

        this.update({ isFiltersVisible });
    }
}
