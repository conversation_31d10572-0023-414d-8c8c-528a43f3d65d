import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';

import { IGeneratedReport } from '../types';
import { ProjectMetrics } from '@core/types';

@Injectable()
export class BookmarksApi {

    constructor(private readonly http: HttpClient) {
    }

    public getReports(params: HttpParams): Observable<HttpResponse<IGeneratedReport[]>> {
        return this.http.get<IGeneratedReport[]>('api/titles/bookmarks', { params, observe: 'response' });
    }

    public getActiveBookmarks(): Observable<ProjectMetrics> {
        return this.http.get<ProjectMetrics>('/api/titles/bookmarks/active');
    }
}
