import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';

import { Observable } from 'rxjs';
import { IFilesForDownload, IPurchasedFile } from '../types';
import { ProjectMetrics } from '@core/types';

@Injectable()
export class PurchaseApi {

    constructor(
        private readonly http: HttpClient,
    ) {
    }

    public getPurchasedFilesList(params: HttpParams): Observable<HttpResponse<IPurchasedFile[]>> {
        return this.http.get<IPurchasedFile[]>('/api/titles/purchased', { params, observe: 'response' });
    }

    public getPurchasedFiles(files: IFilesForDownload[]): Observable<HttpResponse<ArrayBuffer>> {
        return this.http.post('/api/titles/purchased', files, { responseType: 'arraybuffer', observe: 'response' });
    }

    public getActivePurchases(): Observable<ProjectMetrics> {
        return this.http.get<ProjectMetrics>('/api/titles/purchased/active');
    }
}
