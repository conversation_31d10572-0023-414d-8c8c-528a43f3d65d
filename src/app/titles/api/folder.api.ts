/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';

import { Observable } from 'rxjs';
import { IDocument, ImanageParams } from '../types';
import { IResponseStatus } from '@core/types';
import { DocumentApi } from './document.api';

@Injectable()
export class FolderApi {

    constructor(
        private readonly http: HttpClient,
        private readonly documentApi: DocumentApi,
    ) {
    }

    public getFolderStatus(id: string): Observable<IResponseStatus<IDocument>> {
        return this.http.get<IResponseStatus<IDocument>>(`/api/titles/status/${id}`);
    }

    // Imanage
    public logInToImanage(data: any, agentUrl: string | undefined = '', agentSecret: string | undefined = ''): Observable<any> {
        if (agentUrl !== '') {
            const headerDict = {
                'Content-Type': 'application/json',
                'Agent-Key': agentSecret,
            };
            const requestOptions = {
                headers: new HttpHeaders(headerDict),
            };

            return this.http.post(`${agentUrl}/api/imanage/agent/auth/oauth2/token`, data, requestOptions);
        } else {
            return this.http.post('/api/imanage/auth/oauth2/token', data);
        }
    }

    public checkIfAuthorised(): Observable<any> {
        return this.http.get('/api/imanage/authorised', { observe: 'response' });
    }

    public getDialogTokenCall(agentUrl: string | undefined = '', agentSecret: string | undefined, authorization: string): Observable<any> {
        if (agentUrl) {
            const headerDic = {
                'Content-Type': 'application/json',
                'Agent-Authorization': `Bearer ${authorization}`,
                'Agent-Key': agentSecret,
            };
            const requestOptions = {
                headers: new HttpHeaders(headerDic),
            };
            const url = `${agentUrl}/api/imanage/agent/dialog-token`;

            return this.http.get(url, requestOptions);
        } else {
            return this.http.get('/api/imanage/dialog-token');
        }
    }

    public downloadFiles(
        folderId: string,
        confData: ImanageParams,
        listOfOrder: any,
    ): Observable<any> {
        if (confData.agentUrl) {
            const headerDic = {
                'Content-Type': 'application/json',
                'Agent-Authorization': `Bearer ${confData.authorization}`,
                'Agent-Key': confData.agentSecret,
            };
            const requestOptions = {
                headers: new HttpHeaders(headerDic),
                observe: 'response' as const,
            };
            const url = `${confData.agentUrl}/api/imanage/agent/${folderId}/download-from-imanage`;

            return this.http.post(url, listOfOrder, requestOptions);
        } else {
            return this.http.post(`/api/imanage/${folderId}/add`, listOfOrder);
        }
    }

    public isDownloadFinished(confData: ImanageParams, taskId: string): Observable<any> {
        const headerDic = {
            'Content-Type': 'application/json',
            'Agent-Authorization': `Bearer ${confData.authorization}`,
            'Agent-Key': confData.agentSecret,
        };
        const requestOptions = {
            headers: new HttpHeaders(headerDic),
            observe: 'response' as const,
        };
        const url = `${confData.agentUrl}/api/imanage/agent/is-download-finished/${taskId}`;

        return this.http.get(url, requestOptions);
    }

    public downloadFilesToUi(confData: ImanageParams, contentLocation: string): Observable<any> {
        const headerDic = {
            'Content-Type': 'application/json',
            'Agent-Authorization': `Bearer ${confData.authorization}`,
            'Agent-Key': confData.agentSecret,
        };
        const requestOptions = {
            headers: new HttpHeaders(headerDic),
            responseType: 'blob' as const,
        };
        const url = `${confData.agentUrl}${contentLocation}`;

        return this.http.get(url, requestOptions);
    }

    public sendThemToAvail(folderId: string, formData: any): Observable<any> {
        const headerDic = {
            Accept: 'application/json',
        };
        const requestOptions = {
            headers: new HttpHeaders(headerDic),
            observe: 'response' as const,
        };
        const url = this.documentApi.createUploadLink(folderId);

        return this.http.post(url, formData, requestOptions);
    }

    public sendImanageDataToAvail(folderId: string, data: any): Observable<any> {
        return this.http.post(`/api/imanage/${folderId}/add`, data);
    }

    public checkImanageConfiguration(): Observable<any> {
        return this.http.get('/api/imanage/config');
    }
}
