import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MarkerService } from './services/marker.service';
import { MapRequestsTransformationService } from './services/map-requests-transformation.service';
import { MapPopupService } from './services/map-popup.service';
import { MapHoverPopupComponent } from './components/map-hover-popup/map-hover-popup.component';
import { MapFeaturesService } from './services/map-features.service';
import { MapDefaultSourceService } from './services/map-default-source.service';
import { MapNavigationService } from './services/map-navigation.service';
import { MappingApi } from './api/mapping.api';
import { MapComponent } from './components/map/map.component';
import { MaplibreModule } from '../../../maplibre/maplibre.module';
import { MapSnackbarComponent } from './components/map-snackbar/map-snackbar.component';
import { MatIconModule } from '@angular/material/icon';
import { NoPolygonNotificationsService } from './services/no-polygon-notifications.service';
import { GoogleMapsUtilsService } from './services/google-maps-utils.service';
import { MarkerContextMenuService } from './services/marker-context-menu.service';
import { ExportTitlesService } from './services/export-titles.service';
import { UnregisteredZoneService } from './services/unregistered-zone.service';


@NgModule({
    declarations: [
        MapHoverPopupComponent,
        MapComponent,
        MapSnackbarComponent,
    ],
    imports: [
        CommonModule,
        MaplibreModule,
        MatIconModule,
    ],
    providers: [
        MarkerService,
        MapRequestsTransformationService,
        MappingApi,
        MapPopupService,
        MapFeaturesService,
        MapDefaultSourceService,
        MapNavigationService,
        NoPolygonNotificationsService,
        UnregisteredZoneService,
        GoogleMapsUtilsService,
        MarkerContextMenuService,
        ExportTitlesService,
    ],
    exports: [
        MapComponent,
        MapSnackbarComponent,
    ],
})
export class MapSearchModule {
}
