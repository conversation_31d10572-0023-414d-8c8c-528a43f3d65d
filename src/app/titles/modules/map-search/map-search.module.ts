import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MarkerService } from './services/marker.service';
import { MapRequestsTransformationService } from './services/map-requests-transformation.service';
import { MapPopupService } from './services/map-popup.service';
import { MapHoverPopupComponent } from './components/map-hover-popup/map-hover-popup.component';
import { MapFeaturesService } from './services/map-features.service';
import { MapDefaultSourceService } from './services/map-default-source.service';
import { MapNavigationService } from './services/map-navigation.service';
import { MappingApi } from './api/mapping.api';
import { MapComponent } from './components/map/map.component';
import { MaplibreModule } from '../../../maplibre/maplibre.module';
import { MapSnackbarComponent } from './components/map-snackbar/map-snackbar.component';
import { MatIconModule } from '@angular/material/icon';
import { NoPolygonNotificationsService } from './services/no-polygon-notifications.service';
import { GoogleMapsUtilsService } from './services/google-maps-utils.service';
import { MarkerContextMenuService } from './services/marker-context-menu.service';
import { ExportTitlesService } from './services/export-titles.service';
import { UnregisteredZoneService } from './services/unregistered-zone.service';
import { MapFiltersComponent } from './components/map-filters/map-filters.component';
import { SharedModule } from '@shared/shared.module';
import { MapStateWrapperService } from './services/map-state-wrapper.service';
import { MapQueryParamsSyncService } from './services/map-query-params-sync.service';
import { MapFiltersSyncService } from './services/map-filters-sync.service';


@NgModule({
    declarations: [
        MapHoverPopupComponent,
        MapComponent,
        MapSnackbarComponent,
        MapFiltersComponent,
    ],
    imports: [
        CommonModule,
        MaplibreModule,
        MatIconModule,
        SharedModule,
    ],
    providers: [
        MarkerService,
        MapRequestsTransformationService,
        MappingApi,
        MapPopupService,
        MapFeaturesService,
        MapDefaultSourceService,
        MapNavigationService,
        NoPolygonNotificationsService,
        UnregisteredZoneService,
        GoogleMapsUtilsService,
        MarkerContextMenuService,
        ExportTitlesService,
        MapStateWrapperService,
        MapQueryParamsSyncService,
        MapFiltersSyncService,
    ],
    exports: [
        MapComponent,
        MapSnackbarComponent,
        MapFiltersComponent,
    ],
})
export class MapSearchModule {
}
