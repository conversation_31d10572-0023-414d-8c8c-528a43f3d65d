import { Injectable } from '@angular/core';
import { MapElementsBase } from './map-elements-base';
import { LngLat, Map, Marker } from 'maplibre-gl';
import { Observable, Subject } from 'rxjs';
import { defaultSourceId } from '../constants/map-resourses.constants';
import { MarkerContextMenuService } from './marker-context-menu.service';
import { MarkerMenuAction } from '../enums/marker-menu-action.enum';

@Injectable()
export class MarkerService extends MapElementsBase {
    private readonly moveEvent$ = new Subject<LngLat>();
    private readonly markerRemoveEvent$ = new Subject<void>();
    private readonly contextMenuEvent$ = new Subject<MarkerMenuAction>();
    private marker?: Marker;
    private isFeaturesRendering = false;
    private isBlocked = false;

    constructor(
        private readonly contextMenu: MarkerContextMenuService,
    ) {
        super();
        this.featureRenderedEventHandler = this.featureRenderedEventHandler.bind(this);
        this.onMenuActionCall = this.onMenuActionCall.bind(this);
        this.contextMenu.actionEvents().subscribe(this.onMenuActionCall);
    }

    public get contextMenuEvent(): Observable<MarkerMenuAction> {
        return this.contextMenuEvent$.asObservable();
    }

    public get removeEvent(): Observable<void> {
        return this.markerRemoveEvent$.asObservable();
    }

    public get isMarkerOnMap(): boolean {
        return !!this.marker;
    }

    public initialize(map: Map): void {
        super.initialize(map);
        this.connectToMap();
        this.map.on('idle', this.featureRenderedEventHandler);
    }

    public finalize(): void {
        this.map.off('idle', this.featureRenderedEventHandler);
        super.finalize();
    }

    public setIsFeaturesRendering(isFeaturesRendering: boolean): void {
        this.isFeaturesRendering = isFeaturesRendering;
    }

    public block(): void {
        this.isBlocked = true;
    }

    public unblock(): void {
        this.isBlocked = false;
    }

    public onMove(): Observable<LngLat> {
        return this.moveEvent$.asObservable();
    }

    public isMarkerExist(): boolean {
        return !!this.marker;
    }

    public connectToMap(): void {
        if (this.isMarkerExist() && this.isMapInitialized()) {
            this.marker.addTo(this.map);
        }
    }

    public move(point: LngLat): void {
        if (this.isBlocked) {
            return;
        }

        if (!this.isMarkerExist()) {
            this.create(point);
        } else {
            this.marker.setLngLat(point);
        }

        this.moveEvent$.next(point);
    }

    public remove(): void {
        this.contextMenu.hide();
        this.marker?.remove();
        this.marker = null;
        this.markerRemoveEvent$.next();
    }

    public getPosition(): LngLat | null {
        return this.marker?.getLngLat() || null;
    }

    private create(point: LngLat): void {
        this.marker = new Marker().setLngLat(point);
        this.connectToMap();

        this.marker.getElement().addEventListener('contextmenu', (event: MouseEvent): void => {
            event.preventDefault();
            this.contextMenu.show(this.marker);
        });
    }

    private featureRenderedEventHandler(): void {
        if (this.map.isSourceLoaded(defaultSourceId) && this.isFeaturesRendering) {
            this.isFeaturesRendering = false;

            if (this.isMarkerExist()) {
                const position = this.getPosition();
                this.moveEvent$.next(position);
            }
        }
    }

    private onMenuActionCall(action: MarkerMenuAction): void {
        this.contextMenu.hide();
        this.contextMenuEvent$.next(action);

        switch (action) {
            case MarkerMenuAction.remove:
                this.remove();
                break;
            case MarkerMenuAction.viewOnGoogleMaps:
                break;
        }
    }
}
