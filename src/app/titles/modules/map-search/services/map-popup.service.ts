import { ComponentRef, Injectable, ViewContainerRef } from '@angular/core';
import { MapElementsBase } from './map-elements-base';
import { LngLat, Map, MapGeoJSONFeature, Point2D, Popup, PopupOptions } from 'maplibre-gl';
import { debounce, distinctUntilChanged, filter, takeUntil } from 'rxjs/operators';
import { asyncScheduler, Subject, timer } from 'rxjs';
import { EventWithLocation } from '../components/map/map.component';
import { PopupData } from '../types/popup-data.type';
import { MapHoverPopupComponent } from '../components/map-hover-popup/map-hover-popup.component';
import { MapLayer } from '../enums/map-layer.enum';
import { MapFeaturesService } from './map-features.service';

@Injectable()
export class MapPopupService extends MapElementsBase {
    private readonly popupOptions: PopupOptions = {
        closeButton: false,
        closeOnClick: true,
        closeOnMove: true,
        anchor: 'right',
        offset: [0, 0],
    };

    private readonly popupShowing$ = new Subject<EventWithLocation>();
    private readonly popupHidingTimer$ = new Subject<void>();
    private readonly popupHiding$ = new Subject<void>();
    private readonly msBeforeTooltipAppear = 500;
    private readonly msBeforeTooltipDisappear = 10;
    private readonly msTooltipAutoClose = 3000;
    private readonly destroy$ = new Subject<void>();
    private viewContainerRef?: ViewContainerRef;
    private isBlocked = false;
    private componentRef?: ComponentRef<MapHoverPopupComponent>;
    private popup?: Popup;
    private isMouseOverPopup = false;
    private currentHoveredPolygons = 0;

    constructor(
        private readonly mapFeaturesService: MapFeaturesService,
    ) {
        super();
        this.onMouseMove = this.onMouseMove.bind(this);
        this.onMouseEnterPolygon = this.onMouseEnterPolygon.bind(this);
        this.onMouseLeavePolygon = this.onMouseLeavePolygon.bind(this);
    }

    private get isMouseOverPolygon(): boolean {
        return this.currentHoveredPolygons > 0;
    }

    public setRootViewContainerRef(viewContainerRef: ViewContainerRef): void {
        this.viewContainerRef = viewContainerRef;
    }

    public initialize(map: Map): void {
        super.initialize(map);
        this.componentRef = this.viewContainerRef.createComponent(MapHoverPopupComponent);
        this.popup = new Popup(this.popupOptions);
        this.subscribeOnMapEvents();

        this.popupShowing$
            .pipe(
                takeUntil(this.destroy$),
                distinctUntilChanged(),
                debounce(() => timer(this.msBeforeTooltipAppear, asyncScheduler)),
                filter(() => !this.isMouseOverPopup && !this.isBlocked && this.isMouseOverPolygon),
            )
            .subscribe((event) => {
                this.collectDataAndShow(event.lngLat, event.point);
                this.popupHidingTimer$.next();
            });

        this.popupHidingTimer$
            .pipe(
                takeUntil(this.destroy$),
                debounce(() => timer(this.msTooltipAutoClose, asyncScheduler)),
            )
            .subscribe(() => this.popupHiding$.next());

        this.popupHiding$
            .pipe(
                takeUntil(this.destroy$),
                debounce(() => timer(this.msBeforeTooltipDisappear, asyncScheduler)),
                filter(() => !this.isMouseOverPopup),
            )
            .subscribe(() => {
                this.close();
            });

        this.popup.on('close', () => {
            this.isMouseOverPopup = false;
        });
    }

    public block(): void {
        this.isBlocked = true;
    }

    public unblock(): void {
        this.isBlocked = false;
    }

    public finalize(): void {
        this.unsubscribeFromMapEvents();
        this.destroy$.next();
        this.destroy$.complete();
        this.popup?.remove();
        this.popup = null;
        this.componentRef?.destroy();
        this.componentRef = null;
        super.finalize();
    }

    public collectDataAndShow(position: LngLat, point: Point2D): void {
        const popupData = this.extractFeaturesUnderPoint(point);
        this.show(position, popupData);
    }

    public show(position: LngLat, data: PopupData): void {
        const isTitleNumbersExist = data.freeholds?.length || data.leaseholds?.length;
        if (!isTitleNumbersExist) {
            return;
        }

        const popupElement = this.componentRef.location.nativeElement;

        this.componentRef.instance.freeholds = data.freeholds;
        this.componentRef.instance.leaseholds = data.leaseholds;
        this.componentRef.changeDetectorRef.detectChanges();

        this.popup
            .setLngLat(position)
            .setDOMContent(popupElement)
            .addTo(this.map);

        const element = this.popup.getElement();
        element.addEventListener('mouseover', () => {
            this.isMouseOverPopup = true;
        });
        element.addEventListener('mouseout', () => {
            this.isMouseOverPopup = false;
            this.popupHidingTimer$.next();
        });
    }

    public hide(): void {
        this.popupHiding$.next();
    }

    public close(): void {
        this.popup?.remove();
        this.isMouseOverPopup = false;
    }

    private extractFeaturesUnderPoint(point: Point2D): PopupData {
        const features = this.mapFeaturesService.extractFeaturesByPointFromLayers(this.map, point);

        return this.extractPopupData(features);
    }

    private extractPopupData(features: MapGeoJSONFeature[]): PopupData {
        const freeholdsFeatures = features.filter((feature) => feature.properties.tenure === 'Freehold');
        const leaseholdsFeatures = features.filter((feature) => feature.properties.tenure === 'Leasehold');

        const freeholds = freeholdsFeatures.map((feature) => feature.properties.title_number);
        const leaseholds = leaseholdsFeatures.map((feature) => feature.properties.title_number);

        return { freeholds, leaseholds };
    }

    private subscribeOnMapEvents(): void {
        this.map.on('mouseenter', MapLayer.freeholdFill, this.onMouseEnterPolygon);
        this.map.on('mouseleave', MapLayer.freeholdFill, this.onMouseLeavePolygon);
        this.map.on('mouseenter', MapLayer.leaseholdFill, this.onMouseEnterPolygon);
        this.map.on('mouseleave', MapLayer.leaseholdFill, this.onMouseLeavePolygon);
        this.map.on('mousemove', this.onMouseMove);
    }

    private unsubscribeFromMapEvents(): void {
        this.map.off('mouseenter', MapLayer.freeholdFill, this.onMouseEnterPolygon);
        this.map.off('mouseleave', MapLayer.freeholdFill, this.onMouseLeavePolygon);
        this.map.off('mouseenter', MapLayer.leaseholdFill, this.onMouseEnterPolygon);
        this.map.off('mouseleave', MapLayer.leaseholdFill, this.onMouseLeavePolygon);
        this.map.off('mousemove', this.onMouseMove);
    }

    private onMouseEnterPolygon(): void {
        this.increaseCurrentHoveredPolygons();
    }

    private onMouseLeavePolygon(): void {
        this.decreaseCurrentHoveredPolygons();
    }

    private onMouseMove(e: EventWithLocation): void {
        this.popupHiding$.next();

        if (this.isMouseOverPolygon) {
            this.popupShowing$.next(e);
        }
    }

    private increaseCurrentHoveredPolygons(): void {
        this.currentHoveredPolygons++;
    }

    private decreaseCurrentHoveredPolygons(): void {
        this.currentHoveredPolygons--;

        if (this.currentHoveredPolygons < 0) {
            this.currentHoveredPolygons = 0;
        }
    }
}
