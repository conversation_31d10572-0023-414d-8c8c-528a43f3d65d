import { Injectable } from '@angular/core';
import { MapElementsBase } from './map-elements-base';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { LngLat, Map } from 'maplibre-gl';
import { defaultMapCenter, defaultMapZoom } from '../constants/defaults.constants';
import { ProfileService } from '@services';
import { debounceTime, map, takeUntil } from 'rxjs/operators';
import { mapSearchMaxZoom, mapSearchMinZoom } from '../../../constants/land-registry-search.constants';
import { ProjectScopedStorageService } from '../../../../core/services/project-scoped-storage.service';

@Injectable()
export class MapStateWrapperService extends MapElementsBase {
    private readonly mapZoomStorageKey = 'map-zoom';
    private readonly mapCenterStorageKey = 'map-center';
    private readonly storageSyncDebounceMs = 1000;
    private readonly moveEvent$ = new BehaviorSubject<LngLat>(defaultMapCenter);
    private readonly zoomEvent$ = new BehaviorSubject<number>(defaultMapZoom);
    private destroy$ = new Subject<void>();

    constructor(
        private readonly profile: ProfileService,
        private readonly projectScopedStorage: ProjectScopedStorageService,
    ) {
        super();

        this.onMove = this.onMove.bind(this);
        this.onZoom = this.onZoom.bind(this);
    }

    public get center(): LngLat {
        return this.moveEvent$.getValue();
    }

    public get zoom(): number {
        return this.zoomEvent$.getValue();
    }

    public get isZoomAboveMin(): boolean {
        return this.zoomEvent$.getValue() >= this.profile.mapSearchMinZoomFeatureVisibility$.getValue();
    }

    public get selectCenter(): Observable<LngLat> {
        return this.moveEvent$.asObservable();
    }

    public get selectZoom(): Observable<number> {
        return this.zoomEvent$.asObservable();
    }

    public get selectIsZoomAboveMin(): Observable<boolean> {
        return this.zoomEvent$.asObservable()
            .pipe(
                map((zoom) => zoom >= this.profile.mapSearchMinZoomFeatureVisibility$.getValue()),
            );
    }

    public get selectIsMaxZoomAchieved(): Observable<boolean> {
        return this.zoomEvent$.asObservable()
            .pipe(
                map((zoom) => zoom >= mapSearchMaxZoom),
            );
    }

    public get selectIsMinZoomAchieved(): Observable<boolean> {
        return this.zoomEvent$.asObservable()
            .pipe(
                map((zoom) => zoom <= mapSearchMinZoom),
            );
    }

    public setCenter(center: LngLat): void {
        if (this.isMapInitialized()) {
            this.map.setCenter(center);
        } else {
            this.moveEvent$.next(center);
        }
    }

    public setZoom(zoom: number): void {
        if (this.isMapInitialized()) {
            this.map.setZoom(zoom);
        } else {
            this.zoomEvent$.next(zoom);
        }
    }

    public applyMinZoom(): void {
        const minZoom = this.profile.mapSearchMinZoomFeatureVisibility$.getValue();
        this.map.setZoom(minZoom);
    }

    public initialize(map: Map): void {
        super.initialize(map);
        this.destroy$ = new Subject<void>();
        this.subscribeOnMapEvents();
        this.subscribeOnZoomChange();
        this.subscribeOnCenterChange();
    }

    public finalize(): void {
        this.unsubscribeFromMapEvents();
        this.destroy$.next();
        this.destroy$.complete();
        super.finalize();
    }

    public restoreStateFromStorage(): void {
        const zoom = this.projectScopedStorage.loadObject<number>(this.mapZoomStorageKey);
        const center = this.projectScopedStorage.loadObject<LngLat>(this.mapCenterStorageKey);

        if (zoom) {
            this.setZoom(zoom);
        }

        if (center) {
            this.setCenter(center);
        }
    }

    public clearState(): void {
        this.zoomEvent$.next(defaultMapZoom);
        this.moveEvent$.next(defaultMapCenter);
    }

    private subscribeOnMapEvents(): void {
        this.map.on('move', this.onMove);
        this.map.on('zoom', this.onZoom);
    }

    private unsubscribeFromMapEvents(): void {
        this.map.off('move', this.onMove);
        this.map.off('zoom', this.onZoom);
    }

    private onMove(): void {
        const center = this.map.getCenter();
        this.moveEvent$.next(center);
    }

    private onZoom(): void {
        const zoom = this.map.getZoom();
        this.zoomEvent$.next(zoom);
    }

    private subscribeOnZoomChange(): void {
        this.selectZoom
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(this.storageSyncDebounceMs),
                map((zoom) => Math.round(zoom)),
            )
            .subscribe((zoom) => {
                this.projectScopedStorage.saveObject(this.mapZoomStorageKey, zoom);
            });
    }

    private subscribeOnCenterChange(): void {
        this.selectCenter
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(this.storageSyncDebounceMs),
                map((center) => center),
            )
            .subscribe((center) => {
                this.projectScopedStorage.saveObject(this.mapCenterStorageKey, center);
            });
    }
}
