import { Injectable } from '@angular/core';
import { GeoJSONFeature, LngLat, Map, MapGeoJSONFeature, Point2D, PointLike } from 'maplibre-gl';
import { GeoJsonFeatureMap } from '../types/mapping-bounds.type';
import { ShortTitleInfo } from '../../../types';
import { GeometryCollection, MultiPolygon } from 'geojson';
import { MapLayer } from '../enums/map-layer.enum';
import * as turf from '@turf/turf';

@Injectable()
export class MapFeaturesService {

    public extractFeaturesByPoint(map: Map, offset: PointLike, layers: string[]): MapGeoJSONFeature[] {
        return map.queryRenderedFeatures(offset, { layers });
    }

    public convertToMap(features: (GeoJSONFeature | GeoJSONFeature[])[]): GeoJsonFeatureMap {
        return features.reduce((acc, feature) => {
            const titleNumber = Array.isArray(feature)
                ? feature[0].properties.title_number
                : feature.properties.title_number;
            const previousMatchValue: (GeoJSONFeature | GeoJSONFeature[] | undefined) = acc[titleNumber];

            if (previousMatchValue) {
                const newValueInArray = Array.isArray(feature) ? feature : [feature];
                const previousValueInArray = Array.isArray(previousMatchValue) ? previousMatchValue : [previousMatchValue];
                acc[titleNumber] = [...previousValueInArray, ...newValueInArray];
            } else {
                acc[titleNumber] = feature;
            }

            return acc;
        }, {} as { [key: string]: GeoJSONFeature | GeoJSONFeature[] });
    }

    public convertToArray(feature: GeoJsonFeatureMap): GeoJSONFeature[] {
        const list: GeoJSONFeature[] = [];

        Object.values(feature).map((el) => Array.isArray(el) ? list.push(...el) : list.push(el));

        return list;
    }

    public joinUniqByPolyId(a: GeoJSONFeature[], b: GeoJSONFeature[]): GeoJSONFeature[] {
        const result: GeoJSONFeature[] = [...a];

        b.forEach((feature) => {
            const isAlreadyExists = result.some((el) => el.properties.poly_id === feature.properties.poly_id);
            if (!isAlreadyExists) {
                result.push(feature);
            }
        });

        return result;
    }

    public flatCoordinates(array: number[][][][] | number[][][] | number[][]): number[][] {
        const isArrayInside = Array.isArray(array[0][0]);

        if (isArrayInside) {
            return this.flatCoordinates(array[0] as number[][][] | number[][]);
        } else {
            return array as number[][];
        }
    }

    public extractCoordinates(features: GeoJSONFeature[]): number[][] {
        const coordinatesArrays = [];

        features.forEach((feature) => {
            if (feature.geometry.type === 'GeometryCollection') {
                const geometries = (feature.geometry as GeometryCollection).geometries;
                coordinatesArrays.push(...(geometries.map((geometry) => (geometry as MultiPolygon | null)?.coordinates ?? [[]])));
            } else {
                coordinatesArrays.push((feature.geometry as MultiPolygon).coordinates);
            }
        });

        return coordinatesArrays.reduce<number[][]>((acc: number[][], value) => {
            const featureCoordinates = this.flatCoordinates(value);
            acc.push(...featureCoordinates);

            return acc;
        }, []);
    }

    public extractShortTitleInfoFromFeaturesMap(features: GeoJsonFeatureMap): ShortTitleInfo[] {
        const response: ShortTitleInfo[]  = [];

        Object.values(features).map((el) => {
            const isArray = Array.isArray(el);

            if (isArray) {
                const titleNumber = el[0].properties.title_number;
                const tenure = typeof el[0].properties.tenure;
                const polyId = el.map((feature) => feature.properties.poly_id);
                const shortTitleInfo: ShortTitleInfo = {
                    titleNumber: titleNumber,
                    tenure: tenure,
                    polyId: polyId,
                };
                response.push(shortTitleInfo);
            } else {
                const shortTitleInfo: ShortTitleInfo = {
                    titleNumber: el.properties.title_number,
                    tenure: el.properties.tenure,
                    polyId: [el.properties.poly_id],
                };
                response.push(shortTitleInfo);
            }
        });

        return response;
    }

    public extractFeaturesByPointFromLayers(map: Map, offset: Point2D): MapGeoJSONFeature[] {
        const layers = [MapLayer.freeholdFill, MapLayer.leaseholdFill];

        return this.extractFeaturesByPoint(map, [offset.x, offset.y], layers);
    }

    public isFeaturePresentAt(map: Map, offset: Point2D): boolean {
        return !!this.extractFeaturesByPointFromLayers(map, offset).length;
    }

    public getPolygonCenter(geoJSONFeatures: GeoJSONFeature[]): LngLat {
        const coords = this.extractCoordinates(geoJSONFeatures);
        const points = coords.map((coordinate) => turf.point(coordinate));
        const featureCollection = turf.featureCollection(points);
        const center = turf.center(featureCollection);

        return new LngLat(center.geometry.coordinates[0], center.geometry.coordinates[1]);
    }
}
