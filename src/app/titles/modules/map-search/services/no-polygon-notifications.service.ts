import { Injectable } from '@angular/core';
import { MarkerService } from './marker.service';
import { MatSnackBarConfig } from '@angular/material/snack-bar/snack-bar-config';
import { NO_TITLES_UNDER_PIN_TOAST } from '@constants';
import { InfoSnackbarComponent } from '@shared/components/info-snackbar/info-snackbar.component';
import { MatSnackBar, MatSnackBarRef } from '@angular/material/snack-bar';
import { Map } from 'maplibre-gl';
import { Subject } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { MapFeaturesService } from './map-features.service';
import { SelectionToolContextService } from '../../sam/services/selection-tool-context.service';
import { MapSearchQuery } from '../../../store';
import { ProfileService } from '@services';

@Injectable()
export class NoPolygonNotificationsService {
    private readonly destroy$ = new Subject<void>();
    private snackBarRef?: MatSnackBarRef<InfoSnackbarComponent>;
    private map?: Map | null = null;
    private zoom = 0;
    private isMapLoading = false;


    constructor(
        private readonly markerService: MarkerService,
        private readonly snackBar: MatSnackBar,
        private readonly mapFeaturesService: MapFeaturesService,
        private readonly selectionToolContextService: SelectionToolContextService,
        private readonly mapSearchQuery: MapSearchQuery,
        private readonly profile: ProfileService,
    ) {
        this.onZoomChangeEvent = this.onZoomChangeEvent.bind(this);
    }

    public initialize(map: Map): void {
        this.map = map;
        this.zoom = this.map?.getZoom() ?? 0;
        this.destroy$.next();
        this.listenZoomChangeEvent();
        this.listenMarkerRemoveEvent();
        this.listenMapLoadingEvent();
    }

    public finalize(): void {
        this.map = null;
        this.dismissNoTitlesUnderPinSnackBar();
        this.destroy$.next();
        this.isMapLoading = false;
    }

    public handleNoPolygonsMessage(isOverPolygon?: boolean): void {
        const isPinExist = this.markerService.isMarkerOnMap;
        const pinCoordinates = this.markerService.getPosition();
        const isZoomAcceptable = this.zoom >= this.profile.mapSearchMinZoomFeatureVisibility$.getValue();

        if (!isPinExist || !pinCoordinates || !this.map || !isZoomAcceptable) {
            return this.dismissNoTitlesUnderPinSnackBar();
        }

        const pinPosition = this.map?.project(pinCoordinates);
        const isPinOverPolygon = isOverPolygon
            ?? this.mapFeaturesService.isFeaturePresentAt(this.map, pinPosition);
        const isPinWithingLoadedArea = this.selectionToolContextService.isPointInsideShape(pinCoordinates);

        if (this.isMapLoading || !isPinWithingLoadedArea || isPinOverPolygon) {
            return this.dismissNoTitlesUnderPinSnackBar();
        }

        if (isPinWithingLoadedArea && isZoomAcceptable) {
            this.showNoTitlesUnderPinSnackBar();
        }
    }

    private onZoomChangeEvent(): void {
        this.zoom = this.map?.getZoom() ?? 0;
        this.handleNoPolygonsMessage();
    }

    private listenZoomChangeEvent(): void {
        this.map?.on('zoom', this.onZoomChangeEvent);
        this.destroy$.asObservable()
            .pipe(take(1))
            .subscribe(() => this.map?.off('zoom', this.onZoomChangeEvent));
    }

    private listenMarkerRemoveEvent(): void {
        this.markerService.removeEvent
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
                this.dismissNoTitlesUnderPinSnackBar();
            });
    }

    private listenMapLoadingEvent(): void {
        this.mapSearchQuery.selectLoading()
            .pipe(takeUntil(this.destroy$))
            .subscribe((isLoading) => {
                this.isMapLoading = isLoading;

                if (isLoading) {
                    this.handleNoPolygonsMessage();
                }
            });
    }

    private showNoTitlesUnderPinSnackBar(): void {
        if (!this.snackBarRef?.instance) {
            const options: MatSnackBarConfig = {
                data: NO_TITLES_UNDER_PIN_TOAST,
                panelClass: ['info-snackbar', 'min-content', 'min-h-10', 'padding-10'],
            };

            this.snackBarRef = this.snackBar.openFromComponent(InfoSnackbarComponent, options);
            this.snackBarRef.afterDismissed().subscribe(() => this.snackBarRef = null);
        }
    }

    private dismissNoTitlesUnderPinSnackBar(): void {
        this.snackBarRef?.dismiss();
    }
}
