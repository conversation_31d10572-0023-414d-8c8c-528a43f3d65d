import { Injectable } from '@angular/core';
import { MapStateWrapperService } from './map-state-wrapper.service';
import { Subject } from 'rxjs';
import { debounceTime, map, takeUntil } from 'rxjs/operators';
import { UrlParamsService } from '@services';
import { MapSearchQuery } from '../../../store';
import { MarkerService } from './marker.service';
import { mapFilterKeys } from '../types/map-filter-options.type';
import { MapQueryParam, mapUrlQueryParams } from '../enums/map-query-param.enum';

@Injectable()
export class MapQueryParamsSyncService {
    private readonly updateDebounceMs = 500;
    private destroy$ = new Subject<void>();

    constructor(
        private readonly mapStateService: MapStateWrapperService,
        private readonly urlParams: UrlParamsService,
        private readonly mapSearchQuery: MapSearchQuery,
        private readonly markerService: MarkerService,
    ) {
    }

    public initialize(): void {
        this.destroy$ = new Subject<void>();
        this.listenToMapZoomChange();
        this.listenToMapCenterChange();
        this.listenToFiltersChange();
        this.listenToDetailsChange();
        this.listenToMarkerPositionChange();
    }

    public finalize(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    public clearParams(): void {
        this.urlParams.removeParams(mapUrlQueryParams);
    }

    private listenToMapCenterChange(): void {
        this.mapStateService.selectCenter
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(this.updateDebounceMs),
            )
            .subscribe((center) => {
                this.urlParams.addParams({
                    [MapQueryParam.lat]: center.lat,
                    [MapQueryParam.lng]: center.lng,
                });
            });
    }

    private listenToMapZoomChange(): void {
        this.mapStateService.selectZoom
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(this.updateDebounceMs),
                map((zoom) => Math.round(zoom)),
            )
            .subscribe((zoom) => {
                this.urlParams.addParams({ [MapQueryParam.zoom]: zoom });
            });
    }

    private listenToFiltersChange(): void {
        this.mapSearchQuery.select(mapFilterKeys)
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(this.updateDebounceMs),
            )
            .subscribe(({ isFreeholdsOn, isLeaseholdsOn, isNATenureOn }) => {
                this.urlParams.addParams({
                    [MapQueryParam.isFreeholdsOn]: isFreeholdsOn,
                    [MapQueryParam.isLeaseholdsOn]: isLeaseholdsOn,
                    [MapQueryParam.isNATenureOn]: isNATenureOn,
                });
            });
    }

    private listenToDetailsChange(): void {
        this.mapSearchQuery.select('details')
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(this.updateDebounceMs),
                map((details) => details?.titleNumber),
            )
            .subscribe((titleNumber) => {
                if (titleNumber) {
                    this.urlParams.addParams({ [MapQueryParam.titleNumber]: titleNumber });
                } else {
                    this.urlParams.removeParams([MapQueryParam.titleNumber]);
                }
            });
    }

    private listenToMarkerPositionChange(): void {
        this.markerService.onMove()
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(this.updateDebounceMs),
            )
            .subscribe((point) => {
                this.urlParams.addParams({
                    [MapQueryParam.markerLat]: point.lat,
                    [MapQueryParam.markerLng]: point.lng,
                });
            });
        this.markerService.removeEvent
            .pipe(
                takeUntil(this.destroy$),
            )
            .subscribe(() => {
                this.urlParams.removeParams([MapQueryParam.markerLat, MapQueryParam.markerLng]);
            });
    }
}
