import { Injectable } from '@angular/core';
import { MapElementsBase } from './map-elements-base';
import { GeoJSONFeature, LngLat, LngLatBounds, Map } from 'maplibre-gl';
import { MapFeaturesService } from './map-features.service';
import { MarkerService } from './marker.service';
import { ProfileService } from '@services';

@Injectable()
export class MapNavigationService extends MapElementsBase {
    private readonly msMovingAnimationDuration = 500;

    constructor(
        private readonly mapFeaturesService: MapFeaturesService,
        private readonly markerService: MarkerService,
        private readonly profile: ProfileService,
    ) {
        super();
    }

    public initialize(map: Map): void {
        super.initialize(map);
    }

    public finalize(): void {
        super.finalize();
    }

    public pointOn(point?: LngLat): void {
        if (point) {
            this.markerService.move(point);
        } else {
            this.markerService.remove();
        }
    }

    public jumpTo(location: LngLat, zoom?: number): void {
        this.map.jumpTo({
            center: location,
            zoom: zoom || this.profile.mapSearchMinZoomFeatureVisibility$.getValue(),
        });
    }

    public jumpToFeature(features: GeoJSONFeature[]): void {
        const coordinates = this.mapFeaturesService.extractCoordinates(features);
        const bounds = coordinates.reduce(
            (bounds, coordinates) => bounds.extend({ lng: coordinates[0], lat: coordinates[1] }),
            new LngLatBounds(),
        );

        this.map.fitBounds(bounds, { padding: 100, duration: 0 });
    }

    public panTo(point: LngLat): void {
        this.map.panTo(point, { duration: this.msMovingAnimationDuration });
    }
}
