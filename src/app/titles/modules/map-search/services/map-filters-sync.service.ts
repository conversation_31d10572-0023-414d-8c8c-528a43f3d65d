import { Injectable } from '@angular/core';
import { MapStateWrapperService } from './map-state-wrapper.service';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { MapSearchStore } from '../../../store';

@Injectable()
export class MapFiltersSyncService {
    private readonly updateDebounceMs = 100;
    private destroy$ = new Subject<void>();

    constructor(
        private readonly mapStateService: MapStateWrapperService,
        private readonly mapSearchStore: MapSearchStore,
    ) {
    }

    public initialize(): void {
        this.destroy$ = new Subject<void>();
        this.listenToMapZoomChange();
    }

    public finalize(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    private listenToMapZoomChange(): void {
        this.mapStateService.selectIsZoomAboveMin
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(this.updateDebounceMs),
            )
            .subscribe((isZoomAboveMin) => {
                this.mapSearchStore.update({ isFiltersVisible: isZoomAboveMin });
            });
    }
}
