import { Injectable } from '@angular/core';
import { throttleTime } from 'rxjs/operators';
import { Observable, Subject, switchMap } from 'rxjs';
import { environment } from '@env/environment';
import { mapStyleUrl } from '../constants/map-resourses.constants';
import { MappingApi } from '../api/mapping.api';

@Injectable()
export class MapRequestsTransformationService {
    private readonly epsgWebProjectionCode = '3857';
    private readonly proxyServerAccessKeyUpdateEvent$ = new Subject<boolean>();
    private readonly updateProxyServerAccessKey$ = new Subject<void>();
    private folderId?: string;
    private stylesApiKey?: string;

    constructor(
        private readonly mappingApi: MappingApi,
    ) {
        this.transformRequest = this.transformRequest.bind(this);
    }

    public initialize(folderId: string): void {
        this.folderId = folderId;
        this.listenUpdateProxyServerToken();
    }

    public onProxyServerAccessKeyUpdate(): Observable<boolean> {
        return this.proxyServerAccessKeyUpdateEvent$.asObservable();
    }

    public updateProxyServerToken(): void {
        this.updateProxyServerAccessKey$.next();
    }

    public transformRequest(urlString: string): { url: string } {
        if (urlString === mapStyleUrl || urlString.startsWith('/')) {
            return { url: urlString };
        }
        
        try {
            const url = new URL(urlString);
            const newBase = new URL(environment.mapSearch.stylesUrlOrigin ?? location.origin);
            
            url.protocol = newBase.protocol;
            url.hostname = newBase.hostname;
            url.port = newBase.port;

            if (this.stylesApiKey) {
                url.searchParams.set('key', this.stylesApiKey);
            }

            url.searchParams.set('srs', this.epsgWebProjectionCode);

            return { url: url.toString() };
        } catch (error) {
            return { url: urlString };
        }
    }

    private listenUpdateProxyServerToken(): void {
        this.updateProxyServerAccessKey$
            .pipe(
                throttleTime(9000),
                switchMap(() => {
                    return this.mappingApi.getMappingProxyAccessKey(this.folderId);
                }),
            )
            .subscribe((keyObject) => {
                this.stylesApiKey = keyObject.key;
                this.proxyServerAccessKeyUpdateEvent$.next(true);
            });
    }
}
