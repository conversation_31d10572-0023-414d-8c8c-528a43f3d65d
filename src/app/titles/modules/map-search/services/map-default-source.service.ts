import { Injectable } from '@angular/core';
import { MapElementsBase } from './map-elements-base';
import { FilterSpecification, GeoJSONFeature, GeoJSONSource, Map } from 'maplibre-gl';
import { MapLayer } from '../enums/map-layer.enum';
import { defaultSourceId } from '../constants/map-resourses.constants';
import { StringUtilsService } from '@services';
import { MapFeaturesService } from './map-features.service';
import { MarkerService } from './marker.service';
import { GeoJsonFeatureMap } from '../types/mapping-bounds.type';
import { Observable, Subject } from 'rxjs';
import { MapFilterOptions } from '../types/map-filter-options.type';

@Injectable()
export class MapDefaultSourceService extends MapElementsBase {
    public readonly defaultFeatureStyles = {
        fillColor: 'transparent',
        strokeColor: '#BE2535',
        strokeWidth: 1,
    };

    public readonly highlightedFeatureStyles = {
        fillColor: 'rgba(12,106,217,0.1)',
        strokeColor: '#0C6AD9',
    };

    private readonly featuresLoadedEvent$ = new Subject<void>();
    private highlightedFeatureId?: number | string;
    private selectedTitleNumbers: string[] = [];
    private isFeaturesRendering = false;


    constructor(
        private readonly stringUtils: StringUtilsService,
        private readonly mapFeaturesService: MapFeaturesService,
        private readonly markerService: MarkerService,
    ) {
        super();
        this.featuresUpdated = this.featuresUpdated.bind(this);
    }

    public get featuresLoadedEvent(): Observable<void> {
        return this.featuresLoadedEvent$.asObservable();
    }

    public initialize(map: Map): void {
        super.initialize(map);
        this.createGeoJsonSource();
        this.createLayers();
        this.subscribeFeaturesUpdated();
    }

    public finalize(): void {
        this.unsubscribeFeaturesUpdated();
        super.finalize();
    }

    public addFeatures(newFeatures: GeoJsonFeatureMap, oldFeatures: GeoJSONFeature[]): void {
        this.isFeaturesRendering = true;
        const existingFeatures = Object.keys(newFeatures).length
            ? this.mapFeaturesService.convertToArray(newFeatures)
            : oldFeatures;
        const featuresWithId = existingFeatures.map((feature) =>
            Object.assign({
                id: this.stringUtils.hashCode(feature.properties.title_number),
            }, feature),
        );
        this.setFeatures(featuresWithId);
    }

    public createGeoJsonSource(): void {
        const source = this.map.getSource(defaultSourceId);
        if (source) {
            return;
        }

        this.map.addSource(
            defaultSourceId,
            {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: [],
                },
            },
        );
    }

    public createLayers(): void {
        const freeholdTenure = 'Freehold';
        const leaseholdTenure = 'Leasehold';
        const naTenure = 'N/A';

        if (!this.map.getLayer(MapLayer.freeholdFill)) {
            this.createFeatureLayer(MapLayer.freeholdFill, MapLayer.freeholdLine, ['==', ['get', 'tenure'], freeholdTenure]);
            this.createFeatureLayer(MapLayer.leaseholdFill, MapLayer.leaseholdLine, ['==', ['get', 'tenure'], leaseholdTenure]);
            this.createFeatureLayer(MapLayer.naFill, MapLayer.naLine, ['==', ['get', 'tenure'], naTenure]);
        }
    }

    public createFeatureLayer(fillLayerId: MapLayer, lineLayerId: MapLayer, filter: FilterSpecification): void {
        const highlightingFillCondition: any = [
            'case',
            ['boolean', ['feature-state', 'isHighlighted'], false],
            this.highlightedFeatureStyles.fillColor,
            this.defaultFeatureStyles.fillColor,
        ];
        // Polygon layer
        this.map.addLayer({
            id: fillLayerId,
            type: 'fill',
            filter: filter,
            source: defaultSourceId,
            paint: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                'fill-color': highlightingFillCondition,
            },
        });
        // Line of polygon layer
        this.map.addLayer({
            id: lineLayerId,
            type: 'line',
            filter: filter,
            source: defaultSourceId,
            paint: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                'line-color': this.defaultFeatureStyles.strokeColor,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                'line-width': this.defaultFeatureStyles.strokeWidth,
                // 'line-dasharray': [10, 6],
            },
        });
    }

    public updateSelectedFeaturesList(titleNumbers: string[]): void {
        this.selectedTitleNumbers.forEach((titleNumber) => {
            const identifier = { id: this.stringUtils.hashCode(titleNumber), source: defaultSourceId };
            this.map?.setFeatureState(identifier, { 'isSelected': false });
        });
        titleNumbers.forEach((titleNumber) => {
            const identifier = { id: this.stringUtils.hashCode(titleNumber), source: defaultSourceId };
            this.map?.setFeatureState(identifier, { 'isSelected': true });
        });
        this.selectedTitleNumbers = titleNumbers;
    }

    public highlightFeature(titleNumber?: string): void {
        if (this.highlightedFeatureId) {
            this.map?.setFeatureState({ source: defaultSourceId, id: this.highlightedFeatureId }, { 'isHighlighted': false });
        }

        if (titleNumber) {
            const featureId = this.stringUtils.hashCode(titleNumber);
            this.map?.setFeatureState({ source: defaultSourceId, id: featureId }, { 'isHighlighted': true });
            this.highlightedFeatureId = featureId;
        }

        this.updateHighlightedFeatureLineLayer(titleNumber);
    }

    public updateFeaturesFilter(options: MapFilterOptions): void {
        this.updateFeatureLayerOpacity(
            MapLayer.freeholdFill,
            MapLayer.freeholdLine,
            options.isFreeholdsOn,
            options.isZoomAcceptable,
        );
        this.updateFeatureLayerOpacity(
            MapLayer.leaseholdFill,
            MapLayer.leaseholdLine,
            options.isLeaseholdsOn,
            options.isZoomAcceptable,
        );
        this.updateFeatureLayerOpacity(
            MapLayer.naFill,
            MapLayer.naLine,
            options.isNATenureOn,
            options.isZoomAcceptable,
        );
    }

    public updateHighlightedFeatureLineLayer(titleNumber: string): void {
        if (this.map.getLayer(MapLayer.highlightedLine)) {
            this.map.removeLayer(MapLayer.highlightedLine);
        }

        this.map.addLayer({
            id: MapLayer.highlightedLine,
            type: 'line',
            filter: ['==', ['get', 'title_number'], titleNumber],
            source: defaultSourceId,
            paint: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                'line-color': this.highlightedFeatureStyles.strokeColor,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                'line-width': this.defaultFeatureStyles.strokeWidth,
            },
            layout: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                'line-sort-key': 1000,
            },
        });
    }

    public updateFeatureLayerOpacity(fillLayerId: MapLayer, lineLayerId: MapLayer, isFilterOn: boolean, isAvailableZoom: boolean): void {
        const opacitySwitchCase = [
            'case', [
                'any',
                ['boolean', ['feature-state', 'isSelected'], false], // Check property 'isSelected', return a false if null (not set)
                ['all', isAvailableZoom, isFilterOn], // Check filter and zoom states
            ],
            1, // Value of first case
            0, // Default value
        ];

        this.map.setPaintProperty(fillLayerId, 'fill-opacity', opacitySwitchCase);
        this.map.setPaintProperty(lineLayerId, 'line-opacity', opacitySwitchCase);
    }

    public setFeatures(features: GeoJSONFeature[]): void {
        const geoJsonForRender: GeoJSON.GeoJSON = {
            type: 'FeatureCollection',
            features: features,
        };
        this.setSource(geoJsonForRender);
    }

    private setSource(geoJson: string | GeoJSON.GeoJSON): void {
        const source = this.map.getSource(defaultSourceId);
        if (source) {
            this.markerService.setIsFeaturesRendering(true);
            (source as GeoJSONSource).setData(geoJson);
        }
    }

    private subscribeFeaturesUpdated(): void {
        this.map.on('idle', this.featuresUpdated);
    }

    private unsubscribeFeaturesUpdated(): void {
        this.map.off('idle', this.featuresUpdated);
    }

    private featuresUpdated(): void {
        if (this.map.isSourceLoaded(defaultSourceId) && this.isFeaturesRendering) {
            this.isFeaturesRendering = false;
            this.featuresLoadedEvent$.next();
        }
    }
}
