<div class="popup-content scroll-container--thin-scroll">
    <div class="popup-header">Hovered Titles</div>
    <div
        *ngIf="freeholds?.length"
        class="map-info-item"
    >
        <div class="popup-item-head">
            Freehold
        </div>
        <div
            *ngFor="let titleNumber of freeholds"
            class="popup-title-number"
            (click)="copyToClipboard(titleNumber)"
        >
            {{ titleNumber }}
        </div>
    </div>
    <div
        *ngIf="leaseholds?.length"
        class="map-info-item"
    >
        <div class="popup-item-head">
            Leasehold
        </div>
        <div
            *ngFor="let titleNumber of leaseholds"
            class="popup-title-number"
            (click)="copyToClipboard(titleNumber)"
        >
            {{ titleNumber }}
        </div>
    </div>
</div>
