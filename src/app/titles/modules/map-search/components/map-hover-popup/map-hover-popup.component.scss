.popup-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 170px;
    padding: 8px 10px 12px 8px;
    font-weight: 400;
    font-family: Arial, sans-serif;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #fff;
    border-radius: 3px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.popup-header {
    font-size: 14px;
    font-weight: 600;
    color: #4c5263;
    margin-bottom: 2px;
}

.popup-item-head {
    min-width: 73px;
    font-size: 10px;
    line-height: 14px;
    color: #4c5263;
}

.popup-title-number {
    margin-top: 4px;
    text-transform: uppercase;
    font-size: 12px;
    line-height: 16px;
    color: #000113;
}

.popup-title-number:hover {
    opacity: 0.7;
    cursor: pointer;
}

// Override map popup styles
::ng-deep {
    .maplibregl-popup-content {
        padding: 0 8px 0 0;
        background: none;
        box-shadow: none;
    }

    .maplibregl-popup-tip {
        display: none;
    }
}
