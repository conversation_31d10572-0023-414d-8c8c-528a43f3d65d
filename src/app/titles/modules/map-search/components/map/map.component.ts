import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewContainerRef } from '@angular/core';
import { Observable } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { mapSearchMaxZoom, mapSearchMinZoom } from '../../../../constants/land-registry-search.constants';
import { GeoJSONFeature, LngLat, Map, MapMouseEvent, Point2D, RequestTransformFunction } from 'maplibre-gl';
import { MapFeaturesService } from '../../services/map-features.service';
import { MarkerService } from '../../services/marker.service';
import { mapStyleUrl } from '../../constants/map-resourses.constants';
import { MapRequestsTransformationService } from '../../services/map-requests-transformation.service';
import { MapPopupService } from '../../services/map-popup.service';
import { MapDefaultSourceService } from '../../services/map-default-source.service';
import { MapNavigationService } from '../../services/map-navigation.service';
import { SelectionToolContextService } from '../../../sam/services/selection-tool-context.service';
import { LoggerService } from '@services';
import { UnregisteredZoneService } from '../../services/unregistered-zone.service';
import { MapStateWrapperService } from '../../services/map-state-wrapper.service';

export type EventWithLocation = {
    lngLat: LngLat;
    point: Point2D;
}

export type FeatureSelectionEvent = {
    features: GeoJSONFeature[];
    point: LngLat;
}

@Component({
    selector: 'avl-map',
    templateUrl: './map.component.html',
    styleUrls: ['./map.component.scss'],
})
export class MapComponent implements OnInit, OnDestroy {
    public readonly mapSearchMaxZoom = mapSearchMaxZoom;
    public readonly mapSearchMinZoom = mapSearchMinZoom;
    public readonly mapStyleLink = mapStyleUrl;

    @Input()
    public isSelectionUnderPinBlocked = false;

    @Input()
    public isClickable = true;

    @Input()
    public zoom: number;

    @Input()
    public center: LngLat;

    @Output()
    public featuresSelected = new EventEmitter<FeatureSelectionEvent>();

    @Output()
    public bordersChanged = new EventEmitter<void>();

    @Output()
    public mapClicked = new EventEmitter<FeatureSelectionEvent>();

    @Output()
    public mapInitialized = new EventEmitter<Map>();

    @Output()
    public zoomChanged = new EventEmitter<number>();

    @Output()
    public centerChanged = new EventEmitter<LngLat>();

    public isProxyAccessKeyLoaded$: Observable<boolean>;
    public transformRequest?: RequestTransformFunction;

    private map: Map;


    constructor(
        private readonly mapFeaturesService: MapFeaturesService,
        private readonly markerService: MarkerService,
        private readonly mapRequestsTransformationService: MapRequestsTransformationService,
        private readonly mapPopupService: MapPopupService,
        private readonly viewContainerRef: ViewContainerRef,
        private readonly mapDefaultSourceService: MapDefaultSourceService,
        private readonly unregisteredZoneService: UnregisteredZoneService,
        private readonly mapNavigationService: MapNavigationService,
        private readonly selectionToolService: SelectionToolContextService,
        private readonly log: LoggerService,
        private readonly mapStateWrapperService: MapStateWrapperService,
    ) {
    }

    public ngOnInit(): void {
        this.onMapClick = this.onMapClick.bind(this);
        this.mapRequestsTransformationService.updateProxyServerToken();
        this.isProxyAccessKeyLoaded$ = this.mapRequestsTransformationService.onProxyServerAccessKeyUpdate();
        this.transformRequest = this.mapRequestsTransformationService.transformRequest;
    }

    public ngOnDestroy(): void {
        if (this.map) {
            this.mapStateWrapperService.finalize();
            this.unregisteredZoneService.finalize();
            this.markerService.finalize();
            this.mapPopupService.finalize();
            this.selectionToolService.deactivate();
            this.mapDefaultSourceService.finalize();
            this.map.off('click', this.onMapClick);
        }
    }

    public onMapInit(map: Map): void {
        this.map = map;
        this.mapStateWrapperService.initialize(map);
        this.mapDefaultSourceService.initialize(map);
        this.mapNavigationService.initialize(map);
        this.selectionToolService.initialize(map);
        this.mapPopupService.setRootViewContainerRef(this.viewContainerRef);
        this.mapPopupService.initialize(map);
        this.initMarker();
        this.map.on('click', this.onMapClick);

        if (this.unregisteredZoneService.isEnabled) {
            this.unregisteredZoneService.initialize(map);
        }

        this.mapInitialized.emit(map);
    }

    public onMapClick(event: MapMouseEvent): void {
        if (!this.isClickable) {
            return;
        }

        const offsetPoint = event.point;
        const point = event.lngLat;
        const oldPoint = this.markerService.getPosition();
        let selectedFeatures: GeoJSONFeature[] = [];

        if (!oldPoint || oldPoint && point.distanceTo(oldPoint)) {
            selectedFeatures = this.onFeatureSelected(offsetPoint, point);
        }

        this.mapNavigationService.panTo(point);
        this.mapClicked.emit({ features: selectedFeatures, point });
    }

    public onZoomChanged(zoom: number): void {
        this.zoomChanged.emit(zoom);
        this.mapPopupService.hide();
    }

    public onCenterChanged(center: LngLat): void {
        this.centerChanged.emit(center);
    }

    public mapError(errorEvent: ErrorEvent): void {
        const isUnauthorized = errorEvent.error.status === 401;
        if (isUnauthorized) {
            return this.mapRequestsTransformationService.updateProxyServerToken();
        }

        this.log.error(errorEvent.message);
        throw errorEvent.error;
    }

    private onFeatureSelected(offset: Point2D, point: LngLat): GeoJSONFeature[] {
        const features = this.mapFeaturesService.extractFeaturesByPointFromLayers(this.map, offset);
        this.featuresSelected.emit({ features, point });

        return features;
    }

    private selectPolygonsUnderPoint(markerPosition: LngLat): void {
        if (!this.isSelectionUnderPinBlocked) {
            const pointOnMap = this.map.project(markerPosition);
            this.onFeatureSelected(pointOnMap, markerPosition);
        }
    }

    private initMarker(): void {
        this.markerService.initialize(this.map);
        this.markerService.onMove()
            .pipe(
                debounceTime(600),
            )
            .subscribe(this.selectPolygonsUnderPoint.bind(this));
    }
}
