import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { EMPTY, iif, NEVER, Observable, ObservedValueOf, of, OperatorFunction, tap } from 'rxjs';
import {
    AddressSearchResult,
    AddressSearchResultToMap,
    convertToMapSearchType,
    mapAddressSearchResult,
    mapPlaceIdInfo,
    MapSearchResult,
    PlaceIdInfo,
    PlaceIdInfoToMap,
    ShortTitleInfo,
} from '../../../types';
import { catchError, map, mergeMap, shareReplay, switchMap, takeUntil } from 'rxjs/operators';
import { CacheOptions, CacheService, defaultCacheOptions, HttpClientUtilsService } from '@services';
import { GeoJson, MapBounds } from '../types/mapping-bounds.type';
import { HttpRequestCancelSignals } from '../../../../core/utils/http-request-cancel-signals';
import { MapSearchProxyKey } from '../types/map-search-proxy-key.type';
import { LngLat } from 'maplibre-gl';
import { DataWithContentLocation } from '../../../../core/types/data-with-content-location.type';

@Injectable()
export class MappingApi extends HttpRequestCancelSignals {

    constructor(
        private readonly http: HttpClient,
        private readonly placeIdInfoCacheService: CacheService<PlaceIdInfo>,
        private readonly httpUtils: HttpClientUtilsService,
        private readonly titleNumberSearchResultCacheService: CacheService<MapSearchResult[]>,
        private readonly addressSearchResultCacheService: CacheService<AddressSearchResult[]>,
        private readonly selectionQueryCacheService: CacheService<ShortTitleInfo[]>,
        private readonly polygonsFetchCacheService: CacheService<DataWithContentLocation<GeoJson>>,
    ) {
        super();
    }

    public titleNumberSuggestions(query: string, options: CacheOptions = defaultCacheOptions): Observable<MapSearchResult[]> {
        const url = '/api/mapping/query-search';
        const params = new HttpParams()
            .set('query', query);
        const cacheKey = `${url}${params.toString()}`;
        const observable = this.http.get<{
            latitude: number;
            longitude: number;
            text: string;
            type: string;
        }[]>(url, { params })
            .pipe(
                map((items) =>
                    items.map((item) => ({
                        latitude: item.latitude,
                        longitude: item.longitude,
                        searchQuery: item.text,
                        type: convertToMapSearchType(item.type),
                    })),
                ),
            );

        return options?.isCacheEnabled
            ? this.titleNumberSearchResultCacheService.findOrSet(cacheKey, observable.pipe(shareReplay({ bufferSize: 1, refCount: true })))
            : observable;
    }

    public addressSuggestions(query: string, options: CacheOptions = defaultCacheOptions): Observable<AddressSearchResult[]> {
        const url = '/api/mapping/address/autocomplete';
        const params = new HttpParams()
            .set('query', query);
        const cacheKey = `${url}${params.toString()}`;
        const observable = new Observable<AddressSearchResult[]>((subscriber) => {
            this.http.get<AddressSearchResultToMap[]>(url, { params })
                .pipe(
                    map((items) =>
                        items.map((item) => mapAddressSearchResult(item)),
                    ),
                    catchError(() => {
                        subscriber.next([]);

                        return EMPTY;
                    }),
                )
                .subscribe((value) => {
                    subscriber.next(value);
                    subscriber.complete();
                });
        });

        return options?.isCacheEnabled
            ? this.addressSearchResultCacheService.findOrSet(cacheKey, observable.pipe(shareReplay({ bufferSize: 1, refCount: true })))
            : observable;
    }

    public selection(options: {
        isCacheEnabled: boolean;
        titleNumber?: string;
        point?: LngLat;
    } = { isCacheEnabled: false, titleNumber: null, point: null }): Observable<ShortTitleInfo[]> {
        const retryDelay = 200;
        const retryMaxAttempts = 5;
        const cancelSignal = this.createCancelSignal('selection');

        const url = '/api/mapping/selection';
        const params = options.point
            ? new HttpParams()
                .set('lon', options.point.lng)
                .set('lat', options.point.lat)
            : new HttpParams()
                .set('title', options.titleNumber);
        const cacheKey = `${url}${params.toString()}`;

        const observable = this.http.get<{
            // eslint-disable-next-line @typescript-eslint/naming-convention
            title_number: string;
            // eslint-disable-next-line @typescript-eslint/naming-convention
            poly_id: string[];
            tenure: string;
        }[]>(url, { params })
            .pipe(
                map((values) =>
                    values.map((value) => ({
                        titleNumber: value.title_number,
                        polyId: value.poly_id,
                        tenure: value.tenure,
                    })),
                ),
                this.httpUtils.repeatIfServerError(retryMaxAttempts, retryDelay),
            );
        const observableForSaving = observable.pipe(
            catchError(() => {
                this.selectionQueryCacheService.removeValue(cacheKey);

                return NEVER;
            }),
            shareReplay(1),
            takeUntil(cancelSignal),
        );

        return options.isCacheEnabled
            ? this.selectionQueryCacheService.findOrSet(cacheKey, observableForSaving)
            : observable;
    }

    public get(
        bounds?: MapBounds,
        options?: {
            isFreeholdOn?: boolean;
            isLeaseholdOn?: boolean;
            includeTitles?: string[];
            excludeTitles?: string[];
            zoom?: number;
        },
    ): Observable<GeoJson> {
        const retryDelay = 200;
        const retryMaxAttempts = 5;

        const { isFreeholdOn, isLeaseholdOn, includeTitles, excludeTitles, zoom } = {
            ...{
                isFreeholdOn: true,
                isLeaseholdOn: true,
                includeTitles: <string[]> [],
                excludeTitles: <string[]> [],
                zoom: 17,
            },
            ...options,
        };
        const tenure = isFreeholdOn && 'freehold' || isLeaseholdOn && 'leasehold';
        const isFilterOn = isFreeholdOn !== isLeaseholdOn;
        const params = isFilterOn
            ? { ...bounds, tenure }
            : { ...bounds };
        const cancelSignal = this.createCancelSignal('get');
        let urlAndQueryParams = `/api/mapping/geo-search?zoom=${zoom}`;

        if (bounds) {
            urlAndQueryParams += `&latitude_ne=${params.nePoint.lat}`
                + `&latitude_sw=${params.swPoint.lat}`
                + `&longitude_ne=${params.nePoint.lng}`
                + `&longitude_sw=${params.swPoint.lng}`;
        }

        return this.http.post<GeoJson | []>(
            urlAndQueryParams, {
                include: includeTitles.length ? includeTitles : undefined,
                exclude: excludeTitles.length ? excludeTitles : undefined,
            })
            .pipe(
                takeUntil(cancelSignal),
                this.normalizeGeoJson(),
                this.httpUtils.repeatIfServerError(retryMaxAttempts, retryDelay),
            );
    }

    public getByCircle(center: LngLat, radius: number, options: CacheOptions = defaultCacheOptions): Observable<DataWithContentLocation<GeoJson>> {
        const url = '/api/mapping/sim-search';
        const cancelSignal = this.createCancelSignal('getByCircle');
        const retryDelay = 200;
        const retryMaxAttempts = 5;
        const data = {
            type: 'circle',
            center: {
                lng: center.lng,
                lat: center.lat,
            },
            radius,
        };
        const paramsHash = this.polygonsFetchCacheService.hashCode(JSON.stringify(data));
        const cacheKey = `${url}${paramsHash}`;
        const cachedRequest = this.polygonsFetchCacheService.getValue(cacheKey);

        if (options?.isCacheEnabled && cachedRequest) {
            return cachedRequest;
        }

        const cancellationSub = cancelSignal.subscribe(() => this.polygonsFetchCacheService.removeValue(cacheKey));
        const observable = this.http.post<GeoJson | []>(url, data, { observe: 'response' })
            .pipe(
                takeUntil(cancelSignal),
                switchMap((response) => of(response.body)
                    .pipe(
                        this.normalizeGeoJson(),
                        map((geoJson) => {
                            const contentLocation = response.headers.get('Content-Location') || '';

                            return {
                                data: geoJson,
                                contentLocation,
                            };
                        }),
                    ),
                ),
                tap(() => cancellationSub.unsubscribe()),
                this.httpUtils.repeatIfServerError(retryMaxAttempts, retryDelay),
            );

        return options?.isCacheEnabled
            ? this.polygonsFetchCacheService.findOrSet(cacheKey, observable.pipe(shareReplay({ bufferSize: 1, refCount: true })))
            : observable;
    }

    public getByPolygon(points: LngLat[], options: CacheOptions = defaultCacheOptions): Observable<DataWithContentLocation<GeoJson>> {
        const url = '/api/mapping/sim-search';
        const cancelSignal = this.createCancelSignal('getByPolygon');
        const retryDelay = 200;
        const retryMaxAttempts = 5;
        const data = {
            type: 'polygon',
            points: points.map((point) => ({
                lng: point.lng,
                lat: point.lat,
            })),
        };
        const paramsHash = this.polygonsFetchCacheService.hashCode(JSON.stringify(data));
        const cacheKey = `${url}${paramsHash}`;
        const cachedRequest = this.polygonsFetchCacheService.getValue(cacheKey);

        if (options?.isCacheEnabled && cachedRequest) {
            return cachedRequest;
        }

        const cancellationSub = cancelSignal.subscribe(() => this.polygonsFetchCacheService.removeValue(cacheKey));
        const observable = this.http.post<GeoJson | []>(url, data, { observe: 'response' })
            .pipe(
                takeUntil(cancelSignal),
                switchMap((response) => of(response.body)
                    .pipe(
                        this.normalizeGeoJson(),
                        map((geoJson) => {
                            const contentLocation = response.headers.get('Content-Location') || '';

                            return {
                                data: geoJson,
                                contentLocation,
                            };
                        }),
                    ),
                ),
                tap(() => cancellationSub.unsubscribe()),
                this.httpUtils.repeatIfServerError(retryMaxAttempts, retryDelay),
            );

        return options?.isCacheEnabled
            ? this.polygonsFetchCacheService.findOrSet(cacheKey, observable.pipe(shareReplay({ bufferSize: 1, refCount: true })))
            : observable;
    }

    public getDetailsByPlaceId(placeId: string, options: CacheOptions = defaultCacheOptions): Observable<PlaceIdInfo> {
        const url = `/api/mapping/address/place-details/${placeId}`;
        const cancelSignal = this.createCancelSignal('getDetailsByPlaceId');
        const observable = this.http.get<PlaceIdInfoToMap>(url)
            .pipe(
                takeUntil(cancelSignal),
                map((infoData) => mapPlaceIdInfo(infoData)),
            );

        return options?.isCacheEnabled
            ? this.placeIdInfoCacheService.findOrSet(url, observable.pipe(shareReplay(1)))
            : observable;
    }

    public getMappingProxyAccessKey(folderId: string): Observable<MapSearchProxyKey> {
        const url = `/api/mapping/${folderId}/map-search-key`;

        return this.http.get<{
            // eslint-disable-next-line @typescript-eslint/naming-convention
            expires_at: Date;
            // eslint-disable-next-line @typescript-eslint/naming-convention
            folder_id: string;
            key: string;
        }>(url)
            .pipe(
                map((response) => ({
                    expiresAt: response.expires_at,
                    folderId: response.folder_id,
                    key: response.key,
                })),
            );
    }

    private normalizeGeoJson(): OperatorFunction<GeoJson | [], ObservedValueOf<Observable<{ type: string; features: [] } | GeoJson>>> {
        return mergeMap((value) =>
            iif(() => Array.isArray(value),
                of({
                    type: 'FeatureCollection',
                    features: [],
                }),
                of(value as GeoJson),
            ),
        );
    }
}
