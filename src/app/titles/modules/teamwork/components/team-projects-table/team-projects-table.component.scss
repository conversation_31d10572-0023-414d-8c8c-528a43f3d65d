:host {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
}

.team-projects-table-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;

    ::ng-deep &.collection__table-container .loading-table {
        &__row {
            height: 56px !important;
        }
    }

    .collection__table-wrap {
        overflow-y: auto;
        overflow-x: auto;
        flex: 1;
        max-height: calc(100vh - 200px);
        scrollbar-gutter: stable;
    }

    .collection__table {
        .mat-header-row {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #fff;
        }
    }

    ::ng-deep .mat-column {
        &-projectName {
            width: 40%;
            white-space: nowrap;
        }

        &-owner {
            width: 35%;
        }

        &-lastOpenedAt {
            width: 15%;
            min-width: 160px;
        }

        &-titles {
            width: 8%;
            text-align: center;
        }

        &-showMore {
            width: 1%;
            min-width: 24px;
            text-align: right;

            &.mat-header-cell {
                min-width: 132px;
            }
        }

        &-actions {
            width: 1%;
            min-width: 24px;
            padding: 0 28px 0 0 !important;
            text-align: right;

            &.mat-header-cell {
                min-width: 24px;
                box-sizing: content-box;
            }
        }

        ::ng-deep &-actions .loading-table__loader {
            width: 30px !important;

            &:before {
                width: 0 !important;
            }
        }
    }
}

.project-info-column {
    display: flex;
    flex-direction: column;
    white-space: nowrap;
}

.project-name {
    font-size: 14px;
    line-height: 22px;
}

.matter-number {
    font-size: 12px;
    line-height: 20px;
    opacity: 0.5;
}

.show-more-btn {
    display: flex;
    white-space: nowrap;
    align-items: center;
    gap: 10px;
    padding: 0 22px;
    width: 100px;
    height: 30px;
    background-color: #748ece;
    border-radius: 32px;
    color: white;
    text-align: center;
    justify-content: center;
    margin: auto;
}

.show-more-btn {
    background-color: transparent;
    border: 1px solid #748ece;
    color: #748ece;
    text-transform: uppercase;
    padding: 0 18px;
    box-sizing: border-box;
    height: 30px;
    transition:
        color,
        background-color 200ms ease-in-out;

    &--active {
        background-color: #748ece;
        color: white;
    }

    &__icon {
        transition: transform 200ms ease-in-out;
        transform-origin: center center;

        &--spin-up {
            transform: rotate(180deg);
        }
    }
}

.actions-btn {
    transition: background-color 200ms ease-in-out;
    background-color: transparent;
    border-radius: 50%;
    padding: 5px;
    width: 24px;
    height: 24px;

    &:hover {
        background-color: #f6f6f6;
    }

    &__icon {
        width: 24px;
        color: #4c5263;
    }
}

.expanded-details-row {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0;

    color: #96969e;
    font-family: Arial, sans-serif;

    &__title {
        font-size: 14px;
        font-weight: 700;
        line-height: 18px;
        text-transform: uppercase;
    }

    &__content {
        font-size: 12px;
        font-weight: 400;
        line-height: 14px;
    }
}

.details-expandable-row {
    height: auto;

    td {
        height: 0;
        padding: 0;
        border: none;
    }
}
