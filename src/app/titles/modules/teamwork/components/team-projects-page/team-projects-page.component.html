<div class="page">
    <avl-search-bar
        placeholder="Search shared with you projects"
        (searchChanged)="onSearch($event)"
    ></avl-search-bar>
    <div class="table">
        <avl-team-projects-table
            [isLoading]="isLoading$ | async"
            [projects]="projects$ | async"
            [pagination]="pagination$ | async"
            (pageChanged)="onPageChanged($event)"
            (projectOpened)="onProjectOpened($event)"
            (reportDownloaded)="onReportDownloaded($event)"
        ></avl-team-projects-table>
    </div>
</div>
