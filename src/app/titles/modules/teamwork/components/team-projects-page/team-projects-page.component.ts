import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewChild } from '@angular/core';
import { TitleProject } from '../../types/title-project.type';
import { RefreshService, ReportService } from '../../../../services';
import { Router } from '@angular/router';
import { TeamProjectsQuery } from '../../../../../teamwork-common/stores/team-projects.query';
import { TeamProjectsService } from '../../../../../teamwork-common/stores/team-projects.service';
import { BaseTeamProjectsPageComponent } from '../../../../../teamwork-common/components/base-team-projects-page.component';
import { TeamProjectsTableComponent } from '../team-projects-table/team-projects-table.component';

@Component({
    selector: 'avl-team-projects-page',
    templateUrl: './team-projects-page.component.html',
    styleUrls: ['./team-projects-page.component.scss'],
})
export class TeamProjectsPageComponent extends BaseTeamProjectsPageComponent<TitleProject> implements OnInit, OnDestroy {
    @ViewChild(TeamProjectsTableComponent, { static: false })
    public tableComponent: TeamProjectsTableComponent;


    constructor(
        teamProjectsService: TeamProjectsService<TitleProject>,
        teamProjectsQuery: TeamProjectsQuery<TitleProject>,
        private readonly refreshService: RefreshService,
        private readonly router: Router,
        private readonly reportService: ReportService,
    ) {
        super(teamProjectsService, teamProjectsQuery);
    }

    public ngOnInit(): void {
        super.ngOnInit();
    }

    public ngOnDestroy(): void {
        super.ngOnDestroy();
    }

    public onProjectOpened(item: TitleProject): void {
        this.router.navigate(['title/upload'], { queryParams: { fid: item.id } });
        this.refreshService.tryRefresh(item.lastOpenedAt);
    }

    public onReportDownloaded(path: string): void {
        this.reportService.downloadReport(path);
    }

    protected resetPaginationToFirstPage(): void {
        this.tableComponent.resetToFirstPage();
    }

    protected normalizeSortOption(sort: string | undefined): string | null {
        return sort === 'titles' ? 'titleNumbers' : sort;
    }
}
