<div class="purchased-titles">
    <div
        *ngIf="titleNumbers.length > 3"
        class="search"
    >
        <avl-search
            placeholder="Search titles by title number"
            (searchChanged)="onSearch($event)"
            (inputValueChanged)="onInputValueChanged($event)"
        ></avl-search>
    </div>
    <div
        class="titles-container"
        [class.expanded]="isExpanded"
    >
        <div
            class="titles-list"
            [class.long]="isMoreThenOneRow"
        >
            <div
                *ngFor="let item of sortedItems"
                #itemRef
                class="title-item"
                [class.highlighted]="item.isMatched"
                [class.dimmed]="item.isMatched === false"
            >
                {{ item.title }}
            </div>
        </div>

        <button
            *ngIf="isMoreThenOneRow"
            class="show-more-button"
            (click)="toggleExpand()"
        >
            {{ isExpanded ? 'Show less' : 'Show more' }}
        </button>
    </div>
</div>
