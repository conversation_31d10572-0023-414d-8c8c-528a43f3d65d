.purchased-titles {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.search {
    ::ng-deep .avl-search {
        height: 44px;
        border: 1px solid #dcdcdc;
        border-radius: var(--border-radius);
        background-color: white;
        transition: border-color 0.2s ease;

        &:focus-within {
            border: 1px solid #bbbbbb;
        }

        &__input {
            &::placeholder {
                color: rgba(73, 83, 107, 0.3);
            }
        }

        mat-icon {
            --icon-color: rgba(73, 83, 107, 0.3);
        }
    }
}

.titles-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.titles-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    max-height: 30px;
    overflow: hidden;
    transition: max-height 0.5s ease-in-out;

    &.long {
        justify-content: space-between;
    }
}

.title-item {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 7px 9px;
    border-radius: 5px;
    background-color: #f8f8f8;
    min-width: 80px;

    &.highlighted {
        background-color: #65c079;
        color: white;
    }

    &.dimmed {
        opacity: 0.5;
    }
}

.show-more-button {
    width: 100%;
    padding: 10px;
    background-color: #748ece;
    border: 1px solid #748ece;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    color: white;
    transition:
        background-color,
        color,
        opacity 0.2s ease;
    text-transform: uppercase;

    &:hover {
        opacity: 0.7;
    }
}

.expanded {
    .show-more-button {
        background-color: transparent;
        color: #96969e;
    }

    .titles-list {
        max-height: 1200px;
    }
}
