import { Injectable } from '@angular/core';
import { BaseSelection } from './base-selection';
import { DotOptions, PrimitiveElementsService } from './primitive-elements.service';
import { SelectionStrategy } from '../types/selection-strategy.type';
import { PolygonEndSelectionEventData } from '../types/polygon-end-selection-event-data.type';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { ShapeStylesName } from '../enums/shape-styles-name.enum';
import { LngLat, Map, MapMouseEvent } from 'maplibre-gl';
import { SamStateService } from '../stores/sam-state/sam-state.service';
import { SamMode } from '../enums/sam-mode.enum';
import { SelectTool } from '../enums/select-tool.enum';
import { PolygonShapeStyles } from '../types/polygon-shape-styles.type';
import * as turf from '@turf/turf';
import { MapSnackbarStatus } from '../../map-search/enums/map-snackbar-status.enum';
import { MousePositionPopupService } from '../../../../maplibre/services/mouse-position-popup.service';
import { HintsService } from './hints.service';
import { SamHint } from '../enums/sam-hint.enum';
import { Feature, Polygon } from 'geojson';
import { LoggerService } from '@services';
import { ProfileService } from '@services';

@Injectable()
export class PolygonSelectionStrategyService extends BaseSelection implements SelectionStrategy<PolygonEndSelectionEventData> {
    public readonly defaultStyle: PolygonShapeStyles = {
        fillColor: 'rgba(198, 198, 198, 0.38)',
        lineColor: 'rgba(116, 142, 206, 1)',
        drawingBorderColor: 'transparent',
        lineWidth: 1,
        dotStyles: {
            color: 'rgba(116, 142, 206, 1)',
            radius: 2,
            backgroundColor: 'rgba(116, 142, 206, 0.3)',
            backgroundRadius: 7,
        },
        dotHoverStyles: {
            color: 'rgba(136, 206, 116, 1)',
            radius: 5,
            backgroundColor: 'rgba(116, 206, 131, 0.3)',
            backgroundRadius: 17.5,
        },
        lineDash: [],
    };

    public readonly transparencyStyle: PolygonShapeStyles = {
        ...this.defaultStyle,
        fillColor: 'transparent',
        dotStyles: {
            ...this.defaultStyle.dotStyles,
            color: 'transparent',
            backgroundColor: 'transparent',
        },
        dotHoverStyles: {
            ...this.defaultStyle.dotHoverStyles,
            color: 'transparent',
            backgroundColor: 'transparent',
        },
        lineColor: 'rgba(116, 142, 206, 0.9)',
        lineDash: [5, 5],
    };

    public readonly errorStyle: PolygonShapeStyles = {
        ...this.defaultStyle,
        fillColor: 'rgba(246, 97, 110, 0.31)',
        dotStyles: {
            ...this.defaultStyle.dotStyles,
            color: 'rgba(246, 97, 110, 1)',
            backgroundColor: 'rgba(246, 97, 110, 0.31)',
        },
        dotHoverStyles: {
            ...this.defaultStyle.dotHoverStyles,
            color: 'rgba(246, 97, 110, 1)',
            backgroundColor: 'rgba(246, 97, 110, 0.31)',
        },
        lineColor: 'rgba(246, 97, 110, 1)',
    };

    private readonly pointsCountChangeEvent$ = new BehaviorSubject<number>(0);
    private readonly confirmSelectionEvent$ = new Subject<PolygonEndSelectionEventData>();

    private firstPointStyles = {
        color: this.defaultStyle.dotStyles.color,
        backgroundColor: this.defaultStyle.dotStyles.backgroundColor,
        radius: this.defaultStyle.dotStyles.radius,
        backgroundRadius: this.defaultStyle.dotStyles.backgroundRadius,
    };

    private isReadyToClosePolygon = false;
    private isPolygonClosed = false;
    private isError = false;
    private points: LngLat[] = [];
    private cursorPoint?: LngLat = null;
    private styles = this.defaultStyle;
    private validationErrorTimerId?: NodeJS.Timeout = null;

    constructor(
        private readonly primitives: PrimitiveElementsService,
        private readonly samStateService: SamStateService,
        private readonly mouseMapPositionPopupService: MousePositionPopupService,
        private readonly hintsService: HintsService,
        private readonly profile: ProfileService,
        private readonly log: LoggerService,
    ) {
        super();
        this.render = this.render.bind(this);
        this.resizeCanvas = this.resizeCanvas.bind(this);
        this.onMouseMove = this.onMouseMove.bind(this);
        this.onMouseClick = this.onMouseClick.bind(this);
    }

    public isPointInsideShape(pointCoordinates: LngLat): boolean {
        if (!this.points || this.points.length < 3) {
            return false;
        }

        const targetPoint = turf.point([pointCoordinates.lng, pointCoordinates.lat]);
        const polygon = this.getLoadedAreaPolygon();

        return turf.booleanPointInPolygon(targetPoint, polygon);
    }

    public activate(map: Map): void {
        super.activate(map);
        this.subscribeOnMapEvents();
    }

    public startSelecting(): void {
        super.startSelecting();
        this.map.dragPan.disable();
        this.setTargetCursor();
        this.samStateService.updateMode(SamMode.selecting);
        this.hintsService.remove(SamHint.confirmSelectedArea);
        this.mouseMapPositionPopupService.show();

        if (!this.points.length) {
            this.hintsService.add(SamHint.placeFirstPoint);
        }
    }

    public stopSelecting(): void {
        super.stopSelecting();
        this.map.dragPan.enable();
        this.setDefaultCursor();
        this.samStateService.clearMessages();
        this.mouseMapPositionPopupService.hide();
    }

    public clear(): void {
        this.samStateService.updateMode(SamMode.toolSelected);
        this.cursorPoint = null;
        this.isError = false;
        this.points = [];
        this.clearCanvas();
        this.samStateService.markAsPure();
        this.hintsService.cancelPostponedMessage(SamHint.closePolygon);
        this.samStateService.clearMessages();
        this.pointsCountChangeEvent$.next(this.points.length);

        if (this.isPolygonClosed) {
            this.isPolygonClosed = false;
            this.startSelecting();
        }

        this.firstPointStyles = { ...this.styles.dotStyles };
        this.checkIfReadyToClosePolygon();
        this.render();
    }

    public undo(): void {
        if (this.points.length) {
            this.points.pop();
            this.pointsCountChangeEvent$.next(this.points.length);
            this.unmarkError();
            this.samStateService.clearMessages();
            this.hintsService.remove(SamHint.closePolygon);
            this.hintsService.cancelPostponedMessage(SamHint.closePolygon);

            if (this.isPolygonClosed) {
                this.isPolygonClosed = false;
                this.startSelecting();
            }

            this.render();
        }
    }

    public confirmSelection(): void {
        this.samStateService.updateMode(SamMode.displayingPolygons);
        this.hintsService.remove(SamHint.confirmSelectedArea);
        const endEventData: PolygonEndSelectionEventData = {
            type: SelectTool.polygon,
            points: this.points,
        };
        this.confirmSelectionEvent$.next(endEventData);
    }

    public confirmSelectionEvent(): Observable<PolygonEndSelectionEventData> {
        return this.confirmSelectionEvent$.asObservable();
    }

    public pointsCountChangeEvent(): Observable<number> {
        return this.pointsCountChangeEvent$.asObservable();
    }

    public deactivate(): void {
        if (this.isActive) {
            this.clear();
            this.stopSelecting();
            this.unsubscribeFromMapEvents();
            this.samStateService.updateMode(SamMode.noToolSelected);
            this.samStateService.clearMessages();
            super.deactivate();
        }
    }

    public setStyle(styleName: ShapeStylesName): void {
        switch (styleName) {
            case ShapeStylesName.transparency:
                this.styles = this.transparencyStyle;
                break;
            case ShapeStylesName.error:
                this.styles = this.errorStyle;
                break;
            default:
                this.styles = this.defaultStyle;
        }

        this.render();
    }

    public getLoadedAreaPolygon(): Feature<Polygon> | null {
        try {
            if (!this.points || this.points.length < 3) {
                this.log.warn('Cannot create polygon: not enough points');
                return null;
            }

            const polygonCoordinates = this.isPolygonClosed
                ? this.points.map((p) => [p.lng, p.lat])
                : [...this.points.map((p) => [p.lng, p.lat]), [this.points[0].lng, this.points[0].lat]];

            return turf.polygon([polygonCoordinates]);
        } catch (error) {
            this.log.error('Error creating polygon:', error);
            return null;
        }
    }

    public markAsError(): void {
        this.isError = true;
        this.setStyle(ShapeStylesName.error);
    }

    public unmarkError(): void {
        this.isError = false;
        this.setStyle(ShapeStylesName.default);
    }

    protected render(): void {
        if (!this.context) {
            return;
        }

        this.clearCanvas();

        if (this.points.length > 0) {
            const lastPoint = this.points[this.points.length - 1];
            const cursorPointOnCanvas = this.map.project([this.cursorPoint.lng, this.cursorPoint.lat]);
            const lastPointOnCanvas = this.map.project([lastPoint.lng, lastPoint.lat]);

            if (this.isSelection) {
                this.primitives.renderLine(this.context, cursorPointOnCanvas, lastPointOnCanvas, { isDash: true, color: this.styles.lineColor });
            }

            this.checkIfReadyToClosePolygon();
            this.renderPoints();
            this.renderFirstPoint();
            this.connectPointsByLine();
            this.fillSelectedArea();
        }
    }

    private subscribeOnMapEvents(): void {
        this.map.on('move', this.render);
        this.map.on('zoom', this.render);
        this.map.on('click', this.onMouseClick);
        this.map.on('resize', this.resizeCanvas);
        this.map.on('mousemove', this.onMouseMove);
    }

    private unsubscribeFromMapEvents(): void {
        this.map.off('move', this.render);
        this.map.off('zoom', this.render);
        this.map.off('click', this.onMouseClick);
        this.map.off('resize', this.resizeCanvas);
        this.map.off('mousemove', this.onMouseMove);
    }

    private onMouseClick(event: MapMouseEvent): void {
        if (!this.isSelection || this.handlePointClick()) {
            return;
        }

        if (!this.isError) {
            const point = event.lngLat;
            this.samStateService.markAsDirty();
            this.hintsService.remove(SamHint.placeFirstPoint);

            if (!this.isReadyToClosePolygon) {
                this.points.push(point);
                this.pointsCountChangeEvent$.next(this.points.length);
            }

            this.render();
        }
    }

    private onLastPointClick(): void {
        const isSelection = this.isSelection;
        const isNoErrors = !this.isError;
        const isEnoughPoints = this.points.length >= 3;
        const isPolygonValid = this.isPolygonValid({ ignoreCursorPoint: true });
        const lastPoint = this.points[this.points.length - 1];
        const isLastPointCreated = this.isMouseOverPoint(lastPoint);

        if (isEnoughPoints && !isPolygonValid) {
            this.markAsInvalidFor();
            return this.addErrorPolygonTwisted();
        }

        if (isSelection && isNoErrors && isEnoughPoints && isPolygonValid && isLastPointCreated) {
            this.stopSelecting();
            this.samStateService.updateMode(SamMode.waitingConfirmation);
            this.hintsService.remove(SamHint.clickFirstPointToClose);
            this.hintsService.add(SamHint.confirmSelectedArea);
            this.finishPolygon();
            this.render();
        }
    }

    private onMouseMove(event: MapMouseEvent): void {
        this.cursorPoint = event.lngLat;
        this.hintsService.remove(SamHint.closePolygon);
        this.hintsService.cancelPostponedMessage(SamHint.closePolygon);

        if (this.isSelection) {
            if (this.points.length >= 3) {
                this.hintsService.postponeMessage(SamHint.closePolygon);
            }

            this.checkPointsHover();
        } else {
            this.hintsService.remove(SamHint.clickPointToDelete);
        }

        this.checkAreaThreshold();
        this.render();
    }

    private renderPoints(): void {
        const dotStyles: DotOptions = {
            color: this.styles.dotStyles.color,
            backgroundColor: this.styles.dotStyles.backgroundColor,
        };
        // const cursorPointOnCanvas = this.map.project([this.cursorPoint.lng, this.cursorPoint.lat]);

        for (let i = 1; i < this.points.length; i++) { // Skip first point
            const point = this.points[i];
            const pointOnCanvas = this.map.project([point.lng, point.lat]);
            this.primitives.renderDotWithBackground(this.context, pointOnCanvas, dotStyles);
        }

        // this.primitives.renderDotWithBackground(this.context, cursorPointOnCanvas, dotStyles);
    }

    private finishPolygon(): void {
        this.points.push(this.points[0]);
        this.isPolygonClosed = true;

        // this.points = this.polygonTransformationService.buildExteriorPolygon(this.points.map((point)=> point.toArray())).map((points)=> new LngLat(points[0], points[1]));

        this.pointsCountChangeEvent$.next(this.points.length);
        this.fitBoundsToShape(this.getCoordinates());
    }

    private isPolygonValid(options?: { ignoreCursorPoint?: boolean }): boolean {
        const additionalMiddlePoints = options?.ignoreCursorPoint ? [] : [this.cursorPoint];
        const points = this.isPolygonClosed ? [...this.points] : [...this.points, ...additionalMiddlePoints, this.points[0]];

        if (points.length < 4) {
            return false;
        }

        const coords = points.map((point) => point.toArray());
        const polygon = turf.polygon([coords]);

        return !turf.kinks(polygon)?.features.length;
    }

    private addErrorPolygonTwisted(): void {
        const messageId = 'invalid-polygon';

        if (this.validationErrorTimerId) {
            clearTimeout(this.validationErrorTimerId);
            this.validationErrorTimerId = null;
        }

        const message = {
            message: 'Invalid polygon: the polygon should not have any twists',
            status: MapSnackbarStatus.error,
        };
        this.samStateService.updateMessages(message, messageId);
        this.validationErrorTimerId = setTimeout(() => this.samStateService.clearMessages(messageId), 3000);
    }

    private renderFirstPoint(): void {
        if (!this.isSelection) {
            return;
        }

        const pointOnCanvas = this.map.project([this.points[0].lng, this.points[0].lat]);
        const isMaxSizeReached = this.firstPointStyles.radius >= this.defaultStyle.dotHoverStyles.radius;
        const isMinSizeReached = this.firstPointStyles.radius <= this.defaultStyle.dotStyles.radius;
        const isTargetSizeReached = this.isReadyToClosePolygon && isMaxSizeReached
            || !this.isReadyToClosePolygon && isMinSizeReached;

        if (!isTargetSizeReached) {
            this.animatePointSize();
        } else {
            this.firstPointStyles = { ...this.isReadyToClosePolygon ? this.styles.dotHoverStyles : this.styles.dotStyles };
        }

        this.primitives.renderDotWithBackground(this.context, pointOnCanvas, this.firstPointStyles);
    }

    private connectPointsByLine(): void {
        const pointsCount = this.points.length;
        if (pointsCount < 2) {
            return;
        }

        this.context.lineWidth = this.styles.lineWidth;

        for (let i = 1; i < pointsCount; i++) {
            const pointA = this.points[i - 1];
            const pointB = this.points[i];
            const pointAOnCanvas = this.map.project([pointA.lng, pointA.lat]);
            const pointBOnCanvas = this.map.project([pointB.lng, pointB.lat]);

            this.primitives.renderLine(this.context, pointAOnCanvas, pointBOnCanvas, { isDash: !!this.styles.lineDash.length, color: this.styles.lineColor });
        }
    }

    private fillSelectedArea(): void {
        if (this.points.length < 2) {
            return;
        }

        this.context.beginPath();
        const pointAOnCanvas = this.map.project([this.points[0].lng, this.points[0].lat]);
        this.context.moveTo(pointAOnCanvas.x, pointAOnCanvas.y);

        for (let i = 1; i < this.points.length; i++) {
            const pointBOnCanvas = this.map.project([this.points[i].lng, this.points[i].lat]);
            this.context.lineTo(pointBOnCanvas.x, pointBOnCanvas.y);
        }

        const cursorPointOnCanvas = this.map.project([this.cursorPoint.lng, this.cursorPoint.lat]);
        this.context.lineTo(cursorPointOnCanvas.x, cursorPointOnCanvas.y);

        this.context.closePath();
        this.context.fillStyle = this.styles.fillColor;
        this.context.fill();
    }

    private animatePointSize(): void {
        const acceleration = 0.2;
        const radiusRatio = this.styles.dotStyles.radius / this.styles.dotStyles.backgroundRadius;
        const dotRadiusSpeed = acceleration * (this.isReadyToClosePolygon ? 1 : -1);
        const dotBackgroundRadiusSpeed = (acceleration / radiusRatio) * (this.isReadyToClosePolygon ? 1 : -1);

        this.firstPointStyles.radius += dotRadiusSpeed;
        this.firstPointStyles.backgroundRadius += dotBackgroundRadiusSpeed;
        this.firstPointStyles.color = this.styles.dotHoverStyles.color;
        this.firstPointStyles.backgroundColor = this.styles.dotHoverStyles.backgroundColor;

        requestAnimationFrame(this.render.bind(this));
    }

    private checkIfReadyToClosePolygon(): void {
        if (this.points.length < 3) {
            this.isReadyToClosePolygon = false;
            this.hintsService.remove(SamHint.clickFirstPointToClose);
            return;
        }

        const targetDistanceInPx = 14;
        const point = this.points[0];
        this.isReadyToClosePolygon = this.isMouseOverPoint(point, targetDistanceInPx);

        if (this.isReadyToClosePolygon) {
            this.hintsService.add(SamHint.clickFirstPointToClose);
        } else {
            this.hintsService.remove(SamHint.clickFirstPointToClose);
        }
    }

    private checkAreaThreshold(): void {
        if (this.points.length < 2) {
            return;
        }

        const pointsWithMousePoint = [...this.points, this.cursorPoint];
        const polygonPoints = this.isPolygonClosed
            ? this.points.slice()
            : pointsWithMousePoint;

        if (!this.isPolygonClosed) {
            polygonPoints.push(polygonPoints[0]);
        }

        const polygonPositions = polygonPoints.map((coordinates) => [coordinates.lng, coordinates.lat]);
        const polygon = turf.polygon([polygonPositions]);
        const area = turf.area(polygon);
        const maxAreaInM2 = this.profile.samPolygonMaxAreaM2$.getValue();

        if (area > maxAreaInM2) {
            this.addErrorAreaExceeded();
            this.markAsError();
        } else if (this.isError) {
            this.unmarkError();
        } else if (this.isSelection) {
            this.updateAreaInfo(area);
        }
    }

    private updateAreaInfo(area: number): void {
        const roundedArea = Math.round(area);
        const message = {
            message: `${roundedArea} square meters`,
            status: MapSnackbarStatus.measurements,
        };
        this.samStateService.updateMessages(message);
    }

    private addErrorAreaExceeded(): void {
        const maxAreaInM2 = this.profile.samPolygonMaxAreaM2$.getValue();
        const localizedRestrictionsUK = maxAreaInM2.toLocaleString('en-GB');
        const message = {
            message: `The allocated area must not exceed ${localizedRestrictionsUK} square meters`,
            status: MapSnackbarStatus.error,
        };
        this.samStateService.updateMessages(message);
    }

    private getCoordinates(): number[][] {
        return this.points.map(({ lng, lat }) => [lng, lat]);
    }

    private isMouseOverPoint(point: LngLat, targetDistanceInPx: number | undefined = 10): boolean {
        const pointOnCanvas = this.map.project([point.lng, point.lat]);
        const cursorPointOnCanvas = this.map.project([this.cursorPoint.lng, this.cursorPoint.lat]);
        const distance = Math.sqrt(
            Math.pow(cursorPointOnCanvas.x - pointOnCanvas.x, 2) + Math.pow(cursorPointOnCanvas.y - pointOnCanvas.y, 2),
        );

        return distance <= targetDistanceInPx;
    }

    private getIndexOfPointUnderMouse(points: LngLat[]): number {
        return points.findIndex((point) => this.isMouseOverPoint(point, 5));
    }

    private deletePoint(index: number): void {
        this.points = this.points.filter((_, pointIndex) => pointIndex !== index);
    }

    private checkPointsHover(): void {
        const indexOfPointUnderMouse = this.getIndexOfPointUnderMouse(this.points);
        const isPoint = indexOfPointUnderMouse !== -1;
        const isFirstPoint = indexOfPointUnderMouse === 0;
        const isLastPoint = indexOfPointUnderMouse === this.points.length - 1;

        if (isPoint && !isFirstPoint && !isLastPoint) {
            this.setPointerCursor();
            this.hintsService.add(SamHint.clickPointToDelete);
            this.hintsService.cancelPostponedMessage(SamHint.closePolygon);
            this.hintsService.remove(SamHint.closePolygon);
        } else {
            this.isSelection
                ? this.setTargetCursor()
                : this.setDefaultCursor();
            this.hintsService.remove(SamHint.clickPointToDelete);
        }
    }

    private handlePointClick(): boolean {
        const indexOfPointUnderMouse = this.getIndexOfPointUnderMouse(this.points);
        const isPointExist = indexOfPointUnderMouse !== -1;
        const isFirstPoint = indexOfPointUnderMouse === 0;
        const isLastPoint = indexOfPointUnderMouse === this.points.length - 1;

        if (isPointExist && !isFirstPoint && !isLastPoint) {
            this.deletePoint(indexOfPointUnderMouse);
            this.render();
        } else if (isPointExist && isLastPoint) {
            this.onLastPointClick();
        } else if (isFirstPoint || this.isReadyToClosePolygon) {
            if (this.points.length >= 2 && !this.isPolygonValid({ ignoreCursorPoint: true })) {
                this.markAsInvalidFor();
                this.addErrorPolygonTwisted();
            } else {
                this.stopSelecting();
                this.samStateService.updateMode(SamMode.waitingConfirmation);
                this.hintsService.remove(SamHint.clickFirstPointToClose);
                this.hintsService.add(SamHint.confirmSelectedArea);
                this.finishPolygon();
                this.render();
            }
        }

        return isPointExist;
    }

    private markAsInvalidFor(durationMs: number | undefined = 100): void {
        this.markAsError();
        setTimeout(() => this.unmarkError(), durationMs);
    }
}
