import { LngLatBounds, Map } from 'maplibre-gl';

export abstract class BaseSelection {
    protected map?: Map;
    protected canvas?: HTMLCanvasElement;
    protected context?: CanvasRenderingContext2D;
    protected isSelection = false;
    private _isActive = false;

    public get isActive(): boolean {
        return this._isActive;
    }

    public activate(map: Map): void {
        this.map = map;
        this.initCanvas();
        this._isActive = true;
        this.map.doubleClickZoom.disable();
    }

    public startSelecting(): void {
        this.isSelection = true;
    }

    public stopSelecting(): void {
        this.isSelection = false;
    }

    public clearCanvas(): void {
        this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }

    public deactivate(): void {
        this.clearCanvas();
        this.canvas.remove();
        this.map.doubleClickZoom.enable();
        this.canvas = null;
        this.map = null;
        this.context = null;
        this._isActive = false;
    }

    protected initCanvas(): void {
        this.canvas = document.createElement('canvas');
        this.canvas.style.position = 'absolute';
        this.canvas.style.pointerEvents = 'none';
        this.canvas.style.top = '0';
        this.canvas.style.left = '0';

        const mapContainer = this.map.getCanvasContainer();
        mapContainer.appendChild(this.canvas);

        this.context = this.canvas.getContext('2d');

        this.resizeCanvas();
    }

    protected resizeCanvas(): void {
        this.canvas.width = this.map.getCanvas().width;
        this.canvas.height = this.map.getCanvas().height;
        this.render();
    }

    protected abstract render(): void;

    protected setTargetCursor(): void {
        this.map.getCanvasContainer().style.cursor = 'url(assets/icons/icon_target.svg) 12 12, pointer';
    }

    protected setPointerCursor(): void {
        this.map.getCanvasContainer().style.cursor = 'pointer';
    }

    protected setDefaultCursor(): void {
        this.map.getCanvasContainer().style.cursor = null;
    }

    protected fitBoundsToShape(coordinates: number[][]): void {
        if (!coordinates || coordinates.length === 0) {
            return;
        }

        const bounds = new LngLatBounds();
        coordinates.forEach(([lng, lat]) => {
            bounds.extend([lng, lat]);
        });

        this.map.fitBounds(bounds, {
            padding: 100,
            duration: 300,
        });
    }
}
