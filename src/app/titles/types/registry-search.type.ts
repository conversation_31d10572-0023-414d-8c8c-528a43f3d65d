import { Observable } from 'rxjs';
import { HttpResponse } from '@angular/common/http';
import { ITitleInfo, TitleBasketItem } from './index';
import { PurchasedTitleDetails } from './purchased-title-details.type';
import { LandRegistrySearchSource } from '../enums';
import { LandRegistryPurchaseEntity } from '../enums/land-registry-purchase-entity.enum';

export type LandRegistry = {
    getPurchaseEntity: () => LandRegistryPurchaseEntity;
    getIsAvailable: () => Observable<boolean>;
    searchTitle: (folder: string, kind: string, query: string, source: LandRegistrySearchSource) => Observable<ITitleInfo[]>;
    getSearchResults: (url: string) => Observable<HttpResponse<ArrayBuffer>>;
    refreshTitles: (folder: string) => Observable<ITitleInfo[]>;
    purchaseTitles: (folder: string, titles: TitleBasketItem[]) => Observable<HttpResponse<PurchasedTitleDetails[]>>;
    registryUnavailable: () => Observable<void>;
}
