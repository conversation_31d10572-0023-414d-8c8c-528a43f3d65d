import { LngLat } from 'maplibre-gl';
import { SearchRegistry } from '../enums/search-register.enum';
import { RegistrySearchType } from '@enums';
import { MapFilters } from '../modules/map-search/types/map-filter-options.type';

export type LandRegistryDialogOptions = {
    withRefresh?: boolean;
    location?: LngLat;
    zoom?: number;
    filters?: MapFilters;
    titleNumber?: string;
    searchType?: RegistrySearchType;
    searchRegistry?: SearchRegistry;
    noDataDisclaimer?: string;
}
