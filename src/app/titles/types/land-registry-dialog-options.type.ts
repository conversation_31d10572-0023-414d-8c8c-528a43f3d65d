import { LngLat } from 'maplibre-gl';
import { SearchRegistry } from '../enums/search-register.enum';
import { RegistrySearchType } from '@enums';

export type LandRegistryDialogOptions = {
    withRefresh?: boolean;
    location?: LngLat;
    zoom?: number;
    filters?: {
        isFreeholdsOn: boolean;
        isLeaseholdsOn: boolean;
    };
    titleNumber?: string;
    searchType?: RegistrySearchType;
    searchRegistry?: SearchRegistry;
    noDataDisclaimer?: string;
}
