import { IPurchasedFileBase } from './purchased-file.type';
import { ScheduleAction } from '../enums';

export type IScheduleType = {
    key: ScheduleKey;
    name: string;
    isDisabled: boolean;
    isComingSoon: boolean;
    isNew: boolean;
    isBeta: boolean;
    url?: string;
}

export type IScheduleTypeInfo = {
    id?: string;
    key: ScheduleKey;
    type: string;
    description: string;
    unitCost: string;
    totalCost: string;
    hasPreview: boolean;
    provider: string;
    isUpdated: boolean;
    isEmpty: boolean;
    allPurchased: boolean;
    messages: [];
    items: IScheduleItem[];
}

export type IScheduleItem = {
    id: string;
    documentId: string;
    reference?: string;
    titleNumber?: string;
    kind: ScheduleKey;
    class: string;
    ownership: string;
    address: string;
    purchaseCost: number;
    analysisCost: number;
    offerPurchase: boolean;
    offerAnalysis: boolean;
    documentType?: string;
    documentTypeCode?: string;
    documentDate?: string;
    entryNumbers?: string[];
    filedUnder?: string;
    isAvailable: boolean;
    isPurchased: boolean;
    isFiledCopyError: boolean;
    isAnalysisError: boolean;
    errorMessage?: string;
    file?: {
        id: string;
        documentId: string;
        fileName: string;
        folderId: string;
        isError: boolean;
        isProcessed: boolean;
        uploadedAt: string;
    };
}

export type ISchedule = IScheduleTypeInfo & {
    name: string;
    isDisabled: boolean;
    isComingSoon: boolean;
    isNew: boolean;
    isBeta: boolean;
    url?: string;

    // frontend variables
    realProgress?: number;
    displayedProgress?: number;
    isComplete?: boolean;
    isError?: boolean;
    errors: IPurchasedFileBase[];
}

export type ISchedulePurchaseParams = {
    kind: ScheduleKey;
    reference?: string;
    id?: string;
}

export enum ScheduleKey {
    titlePlan = 'title-plan',
    companiesHouse = 'companies-house',
    appEnquiry = 'app-enquiry',
    discharge = 'discharge',
    linkedDocuments = 'linked-documents',
    digitalTitlePlan = 'digital-title-plan',
    linkedDocumentsAnalysis = 'linked-documents-analysis',
    otherSearches = 'other-searches',
    localSearches = 'local-searches',
    compiledDigitalTitlePlan = 'compiled-digital-title-plan',
    planning = 'planning',
    epc = 'epc',
    digitalMapping = 'digital-mapping',
    scottishBurdenSummary = 'scottish-burden-summary',
    scottishCompaniesHouse = 'scottish-companies-house',
    scottishTitlePlan = 'scottish-title-plan',
    scottishAppEnquiry = 'scottish-app-enquiry',
}

export type ScheduleActionEvent = {
    schedule: ISchedule;
    action: ScheduleAction;
};
