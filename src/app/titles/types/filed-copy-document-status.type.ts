import { InfoMessage } from './info-message.type.js';

export type FiledCopyDocumentStatus = {
    id: string;
    address: string;
    fileName: string;
    isAccepted: boolean;
    isProcessed: boolean;
    isError: boolean;
    titleNumber: string;
    linkedTo: string | null;
    messages: InfoMessage[];
    metadata: {
        dateUploaded?: string;
        fileName?: string;
        pages?: number;
        size?: string;
    };
    type: string;
    uploadedAt: string;
}
