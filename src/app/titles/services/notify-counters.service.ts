import { Injectable } from '@angular/core';
import { BookmarksApi } from '../api';
import { forkJoin, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { NotifyCountersStore } from '../store';
import { ProjectMetrics } from '@core/types';
import { PurchaseService } from './purchase.service';
import { TeamworkApi } from '../../teamwork-common/api/teamwork.api';
import { TitleProject } from '../modules/teamwork/types/title-project.type';

@Injectable()
export class NotifyCountersService {

    constructor(
        private readonly bookmarksApi: BookmarksApi,
        private readonly purchaseService: PurchaseService,
        private readonly teamworkApi: TeamworkApi<TitleProject>,
        private readonly notifyCountersStore: NotifyCountersStore,
    ) {
    }

    public getCounters(): Observable<[ProjectMetrics, ProjectMetrics, ProjectMetrics]> {
        return forkJoin([
            this.purchaseService.getActivePurchases(),
            this.bookmarksApi.getActiveBookmarks(),
            this.teamworkApi.getMetrics(),
        ])
            .pipe(
                tap((response) => {
                    this.notifyCountersStore.update({
                        purchased: response[0],
                        bookmarks: response[1],
                        teamProjects: response[2],
                    });
                }),
            );
    }
}
