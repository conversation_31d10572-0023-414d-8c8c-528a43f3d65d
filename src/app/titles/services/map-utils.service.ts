import { Injectable } from '@angular/core';
import { LngLat } from 'maplibre-gl';
import { MapBounds } from '../modules/map-search/types/mapping-bounds.type';
import { bboxPolygon, booleanPointInPolygon } from '@turf/turf';
import { point as turfPoint } from '@turf/helpers';

@Injectable()
export class MapUtilsService {
    public isPreviousBoundsIncludeNew(previousBounds: MapBounds, newBounds: MapBounds): boolean {
        const previousLatNe = previousBounds.nePoint.lat;
        const previousLatSw = previousBounds.swPoint.lat;
        const previousLongNe = previousBounds.nePoint.lng;
        const previousLongSw = previousBounds.swPoint.lng;

        const newLatNe = newBounds.nePoint.lat;
        const newLatSw = newBounds.swPoint.lat;
        const newLongNe = newBounds.nePoint.lng;
        const newLongSw = newBounds.swPoint.lng;

        return previousLatNe >= newLatNe
            && previousLongNe >= newLongNe
            && previousLatSw <= newLatSw
            && previousLongSw <= newLongSw;
    }

    public isPointsDifferent(a?: LngLat, b?: LngLat): boolean {
        const isPointAExists = !!a;
        const isPointBExists = !!b;
        const isLngDifferent = a?.lng !== b?.lng;
        const isLatDifferent = a?.lat !== b?.lat;

        return !isPointAExists || !isPointBExists || isLngDifferent || isLatDifferent;
    }

    public isPointInsideBounds(point: LngLat, bounds: MapBounds): boolean {
        const bbox: [number, number, number, number] = [
            bounds.swPoint.lng,
            bounds.swPoint.lat,
            bounds.nePoint.lng,
            bounds.nePoint.lat,
        ];
        const polygon = bboxPolygon(bbox);
        const turfPt = turfPoint([point.lng, point.lat]);

        return booleanPointInPolygon(turfPt, polygon);
    }
}
