import { Injectable } from '@angular/core';
import { SchedulesQuery } from '../store';
import { ISchedule, ScheduleKey } from '../types';
import { ScheduleAction, SearchType, UploadDialogsStatus } from '../enums';
import { DownloadSchedulePreviewDialogComponent } from '../components/dialogs/download-schedule-preview-dialog/download-schedule-preview-dialog.component';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ProfileService } from '@services';
import { OnboardingManageService } from '../../onboarding/services';
import { LinkedDocumentAnalysisService } from './linked-document-analysis.service';
import { FiledCopiesDialogComponent } from '../components/dialogs/filed-copies-dialog/filed-copies-dialog.component';
import { forkJoin, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { FiledCopiesDialogV2Component } from '../components/dialogs/filed-copies-dialog-v2/filed-copies-dialog-v2.component';
import { UploadSearchesDialogComponent } from '../components/dialogs/upload-searches-dialog/upload-searches-dialog.component';
import { schedulesCostThresholdForWarning } from '../constants';
import { PURCHASE_A_LOT_OF_TITLES } from '@constants';
import { ConfirmDialogComponent } from '@shared/components/dialogs/confirm-dialog/confirm-dialog.component';
import { SchedulesService } from '../store/schedules/schedules.service';
import { ProjectDetailsQuery } from '../../project-details/stores/project-details/project-details.query';

@Injectable()
export class SchedulesTableService {

    constructor(
        private readonly schedulesService: SchedulesService,
        private readonly dialog: MatDialog,
        private readonly profileService: ProfileService,
        private readonly onboardingService: OnboardingManageService,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly linkedDocumentsAnalysisService: LinkedDocumentAnalysisService,
        private readonly schedulesQuery: SchedulesQuery,
    ) {
    }

    public handleAction(schedule: ISchedule, action: ScheduleAction): void {
        switch (action) {
            case ScheduleAction.showUploadFilesModal:
                this.showUploadFilesModal(schedule);
                break;

            case ScheduleAction.showPreview:
                if (this.onboardingService.isActive) {
                    return;
                }
                this.dialog.open(DownloadSchedulePreviewDialogComponent, {
                    panelClass: ['schedule-preview-dialog'],
                    data: { key: schedule.key, title: schedule.type },
                });
                break;

            case ScheduleAction.showAnalysingModal:
                break;

            case ScheduleAction.startPurchase:
                this.checkTotalCostAndStartPurchase(schedule);
                break;

            case ScheduleAction.getLinkedDocItems:
                const folderId = this.projectDetailsQuery.projectId;
                this.schedulesService.fetchLinkedDocumentItems(folderId);
                break;
            case ScheduleAction.showSelectLinkedDocument:
                if (this.onboardingService.isActive) {
                    return;
                }

                const isOneClickPurchaseEnabled = this.profileService.isOneClickFiledCopiesEnabled$.getValue();
                if (isOneClickPurchaseEnabled) {
                    this.openLinkedDocumentsSelectDialogV2(schedule);
                } else {
                    this.openLinkedDocumentsSelectDialog();
                }

                break;
        }
    }

    private openLinkedDocumentsSelectDialog(): void {
        const dialogRef = this.dialog.open(FiledCopiesDialogComponent, {
            panelClass: ['filed-copies-dialog'],
            width: '100%',
            minWidth: '90%',
        });
        dialogRef.afterClosed()
            .subscribe(() => this.schedulesService.recalculatePrice());
    }

    private openLinkedDocumentsSelectDialogV2(schedule: ISchedule): void {
        (document.activeElement as HTMLElement)?.blur();

        const folderId = this.projectDetailsQuery.projectId;

        const updateLinkedDocuments = (): Observable<void> => this.schedulesService.fetchSchedules(folderId, [ScheduleKey.linkedDocuments])
            .pipe(map(() => void 0));
        const dialogRef = this.dialog.open(FiledCopiesDialogV2Component, {
            panelClass: ['filed-copies-dialog', 'filed-copies-dialog-v2'],
            disableClose: true,
            data: {
                schedule,
                updateLinkedDocuments,
            },
        });
        dialogRef.afterClosed()
            .subscribe((isDocumentPurchased) => {
                if (isDocumentPurchased) {
                    forkJoin([
                        this.schedulesService.fetchSchedules(folderId, [ScheduleKey.linkedDocuments]),
                        this.schedulesService.fetchSchedules(folderId, [ScheduleKey.linkedDocumentsAnalysis]),
                    ])
                        .subscribe(async () => {
                            const isPositive = await this.linkedDocumentsAnalysisService.tryToOpenFiledCopiesAnalysisDialog();
                            if (isPositive) {
                                const filedCopiesAnalysis = this.schedulesQuery.getEntity(ScheduleKey.linkedDocumentsAnalysis);
                                this.handleAction(filedCopiesAnalysis, ScheduleAction.startPurchase);
                                this.linkedDocumentsAnalysisService.emitAnalysisStartedEvent();
                            }
                        });
                }
            });
    }

    private showUploadFilesModal(schedule: ISchedule): void {
        switch (schedule.key) {
            case ScheduleKey.localSearches:
                this.openSearchesModal(SearchType.local)
                    .afterClosed()
                    .subscribe((status: UploadDialogsStatus) => {
                        if (status === UploadDialogsStatus.startProcessing) {
                            this.schedulesService.startSearchDocumentsProcessing(schedule);
                        }
                    });
                break;
            case ScheduleKey.otherSearches:
                this.openSearchesModal(SearchType.other)
                    .afterClosed()
                    .subscribe((status: UploadDialogsStatus) => {
                        if (status === UploadDialogsStatus.startProcessing) {
                            this.schedulesService.startSearchDocumentsProcessing(schedule);
                        }
                    });
                break;
        }
    }

    private openSearchesModal(type: SearchType): MatDialogRef<UploadSearchesDialogComponent> {
        return this.dialog.open(UploadSearchesDialogComponent, {
            panelClass: 'upload-searches-dialog',
            width: '100%',
            data: { type },
        });
    }

    private checkTotalCostAndStartPurchase(schedule: ISchedule): void {
        const totalCostString = schedule && schedule.totalCost;
        const totalCostNumber = totalCostString && parseFloat(totalCostString.slice(1));
        const isTotalCostTooMuch = !Number.isNaN(totalCostNumber) && totalCostNumber >= schedulesCostThresholdForWarning;

        if (isTotalCostTooMuch) {
            const data = {
                ...PURCHASE_A_LOT_OF_TITLES,
                subtitle: `You will incur a cost of ${totalCostString} on your client/matter file.`,
            };
            this.dialog.open(ConfirmDialogComponent,
                { panelClass: 'confirm-dialog', data },
            )
                .afterClosed()
                .subscribe((isConfirm: boolean) => {
                    if (!isConfirm) {
                        return;
                    }
                    this.schedulesService.purchase(schedule);
                });
        } else {
            this.schedulesService.purchase(schedule);
        }
    }
}
