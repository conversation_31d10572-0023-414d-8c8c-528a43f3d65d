import { Injectable } from '@angular/core';
import { BaseLandRegistryService } from './base-land-registry.service';
import { HmlrApi } from '../../api';
import { Observable, of } from 'rxjs';
import { ITileNumberForPurchase, ITitleInfo, LandRegistry, TitleBasketItem } from '../../types';
import { switchMap } from 'rxjs/operators';
import { HttpResponse } from '@angular/common/http';
import { PurchasedTitleDetails } from '../../types/purchased-title-details.type';
import { LandRegistrySearchSource } from '../../enums';
import { LandRegistryPurchaseEntity } from '../../enums/land-registry-purchase-entity.enum';
import { SearchResultsStore } from '../../store';

@Injectable()
export class HmLandRegistryService extends BaseLandRegistryService implements LandRegistry {

    constructor(
        private readonly hmlrApi: HmlrApi,
        private readonly searchResultStore: SearchResultsStore,
    ) {
        super(hmlrApi, searchResultStore);
    }

    public getPricing(): Observable<number> {
        return this.hmlrApi.getPricing();
    }

    public getPurchaseEntity(): LandRegistryPurchaseEntity {
        return LandRegistryPurchaseEntity.hmlr;
    }

    public getIsAvailable(): Observable<boolean> {
        return of(true);
    }

    public searchTitle(folder: string, kind: string, query: string, source: LandRegistrySearchSource): Observable<ITitleInfo[]> {
        return this.search(folder, kind, query, source);
    }

    public refreshTitles(folder: string): Observable<ITitleInfo[]> {
        return this.refresh(folder);
    }

    public purchaseTitles(folder: string, titles: TitleBasketItem[]): Observable<HttpResponse<PurchasedTitleDetails[]>> {
        const titleNumbers: ITileNumberForPurchase[] = titles.map((item) => ({
            kind: 'title-register',
            reference: item.titleNumber,
        }));

        return this.hmlrApi.purchase(folder, titleNumbers)
            .pipe(
                switchMap((url) => this.getPurchaseStatus(url)),
            );
    }

    public registryUnavailable(): Observable<void> {
        return of();
    }
}
