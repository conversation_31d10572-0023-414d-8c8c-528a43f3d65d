import { Routes } from '@angular/router';

import { TitleRegistersComponent } from '../components/title-registers/title-registers.component';
import { TitlesResolve } from '../resolvers/titles.resolve';
import { TitlesUploadComponent } from '../components/titles-upload/titles-upload.component';
import { PurchasedPageComponent } from '../components/purchased-page/purchased-page.component';
import { ReportsPageComponent } from '../components/reports-page/reports-page.component';
import { TitlesSchedulesTableComponent } from '../components/titles-schedules/titles-schedules.component';
import { SchedulesResolve } from '../resolvers/schedules.resolve';
import { ThemesResolve } from '../resolvers/themes.resolve';
import { DocumentViewerComponent } from '@shared/components/document-viewer/document-viewer.component';
import { NotFoundPageComponent } from '@shared/components/not-found-page/not-found-page.component';
import { InitiateOnboardingResolver } from '../../onboarding/resolvers/initiate-onboarding.resolver';
import { ConfirmExitOnboardingGuard } from '../../onboarding/guards/confirm-exit-onboarding.guard';
import { ProjectDetailsResolver } from '../../project-details/resolvers/project-details.resolver';
import { TeamProjectsPageComponent } from '../modules/teamwork/components/team-projects-page/team-projects-page.component';

export const titlesRoutes: Routes = [
    {
        path: '',
        resolve: { isOnboardingGoing: InitiateOnboardingResolver },
        component: TitleRegistersComponent,
        children: [
            {
                path: '',
                redirectTo: 'upload',
                pathMatch: 'full',
            },
            {
                path: 'upload',
                component: TitlesUploadComponent,
                resolve: {
                    folder: TitlesResolve,
                    isOldTheme: ThemesResolve,
                    project: ProjectDetailsResolver,
                },
            },
            {
                path: 'upload/:imanage',
                component: TitlesUploadComponent,
                resolve: {
                    folder: TitlesResolve,
                    isOldTheme: ThemesResolve,
                    project: ProjectDetailsResolver,
                },
            },
            {
                path: 'schedules',
                component: TitlesSchedulesTableComponent,
                canDeactivate: [ConfirmExitOnboardingGuard],
                resolve: {
                    folder: TitlesResolve,
                    schedules: SchedulesResolve,
                    project: ProjectDetailsResolver,
                },
            },
            {
                data: { pageTitle: 'Purchased Files' },
                path: 'purchased',
                component: PurchasedPageComponent,
            },
            {
                data: { pageTitle: 'Previous Projects' },
                path: 'reports',
                component: ReportsPageComponent,
            },
            {
                data: { pageTitle: 'Team Projects' },
                path: 'team-projects',
                component: TeamProjectsPageComponent,
            },
        ],
    },
    {
        path: 'filed-copies',
        children: [
            {
                path: ':documentId',
                data: {
                    resourceUrlWithPlaceholders: 'api/filed-copies/document/:folderId/:documentId',
                    resourceNotFoundPage: 'title/documents/not-found',
                    folderPageUrl: '/title/schedules?fid=:folderId',
                },
                component: DocumentViewerComponent,
            },
        ],
    },
    {
        path: 'documents',
        children: [
            {
                path: 'not-found',
                component: NotFoundPageComponent,
                data: {
                    logoUrl: '/assets/images/avail-titles-logo.svg',
                    title: 'Document Not Found',
                    description: 'This document has been deleted or moved.<br>Click Back to Project to return to your project or the home page.<br>Alternatively, select Message Us to contact our support team for assistance.',
                    backButtonText: 'Back to Project',
                },
            },
            {
                path: ':documentId',
                data: {
                    resourceUrlWithPlaceholders: 'api/titles/document/:folderId/:documentId',
                    resourceNotFoundPage: 'title/documents/not-found',
                    folderPageUrl: '/title/upload?fid=:folderId',
                },
                component: DocumentViewerComponent,
            },
        ],
    },
];
