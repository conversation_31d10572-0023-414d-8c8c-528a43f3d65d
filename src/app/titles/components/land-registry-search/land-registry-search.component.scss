@import '~assets/sass/variables';
@import '~assets/sass/mixins';

.form {
    display: flex;
    justify-content: center;
    height: 56px;

    .mat-form-field {
        padding: 8px 16px;
        line-height: 40px;
        background-color: $mid-gray;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;

        .mat-select-arrow {
            color: #979797;
        }

        &.mat-focused.mat-primary {
            .mat-select-arrow {
                color: #979797;
            }
        }
    }

    .mat-form-field-appearance-legacy .mat-form-field-wrapper {
        padding: 0;
    }

    .mat-form-field-infix {
        width: 158px;
        padding: 0;
        border: none;
    }

    .mat-form-field-underline {
        display: none;
    }

    .mat-select-arrow-wrapper {
        padding: 0;
    }

    .mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
        color: rgba(0, 0, 0, 0.87);
    }

    .mat-select-value {
        color: $black;
    }

    &.disable-events {
        pointer-events: none;
    }
}

.search-container {
    --button-width: 56px;
    position: relative;
    display: flex;
    width: 100%;
}

.label {
    display: flex;
    width: 100%;
    background-color: #e7e7e7;
    text-align: left;
}

.input {
    width: 100%;
    padding: 8px calc(var(--button-width) + 8px) 8px 16px;
    border: none;
    background-color: transparent;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0.44px;

    @include input-placeholder {
        color: $dark-gray;
    }
}

.search-button {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 100;
    display: flex;
    width: var(--button-width);
    height: 100%;
    padding: 0;
    border-radius: 0 4px 4px 0;

    .mat-icon {
        margin: auto;
        width: 24px;
        height: 24px;
    }
}

.autocomplete {
    &.mat-autocomplete-panel {
        margin-top: 4px;
        max-height: 331px;
        box-shadow: none;
        border: 1px solid #d6d7dd;
        border-radius: 0 4px 4px 4px;

        .mat-optgroup-label,
        .mat-optgroup .mat-option {
            height: auto;
            font-family: 'Arial', sans-serif;
            font-size: 16px;
            line-height: 18px;
            color: #000113;
        }

        .mat-optgroup-label {
            padding-top: 12px;
            padding-bottom: 10px;
            font-weight: 700;
        }

        .mat-optgroup .mat-option {
            padding: 12px 26px;
            font-weight: 400;
        }
    }
}
