:host {
    display: flex;
    flex: 1;
    flex-direction: column;
}

.reports-table {
    &__report-btn,
    &__open-btn {
        padding: 8px 10px;
        height: 32px;
        width: 100%;
    }

    &__open-btn {
        max-width: 88px;
    }

    &__report-btn {
        max-width: 104px;
    }

    ::ng-deep .mat-column {
        &-report {
            width: 1%;
            min-width: 104px;
            text-align: right;

            &.mat-header-cell {
                min-width: 136px;
            }
        }

        &-open {
            width: 1%;
            padding-left: 0;
            min-width: 88px;
            text-align: right;

            &.mat-header-cell {
                min-width: 112px;
            }
        }

        &-projectName {
            width: 20%;
        }

        &-matter {
            width: 20%;
        }

        &-createdAt {
            width: 20%;
        }

        &-lastOpenedAt {
            width: 20%;
        }
    }
}

.no-wrap {
    white-space: nowrap;
}
