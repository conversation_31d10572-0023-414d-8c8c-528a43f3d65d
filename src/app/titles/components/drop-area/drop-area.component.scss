@use 'assets/sass/variables' as *;

$grey: #96969e;

:host {
    flex: 1 100%;
    min-height: 278px;
    margin-bottom: 16px;
}

.drop-area {
    display: flex;
    height: 100%;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-top: 30px;
    padding-bottom: 30px;
    border-radius: 5px;
    border: 2px dashed $grey;
    background-color: #ebebeb;
    overflow: hidden;
    margin: auto;
    text-align: center;
    transition: background-color 0.2s ease;

    &__wrap {
        height: 100%;
    }

    &.file-over {
        background-color: white;
        border-color: $light-blue;
    }

    &__icon {
        width: 56px;
        height: 56px;
        margin-bottom: 24px;
    }

    &__title {
        margin-bottom: 8px;
        font-size: 16px;
        color: #4c5263;
    }

    &__description {
        margin: 0 auto 24px;
        font-size: 13px;
        width: 175px;
        color: $grey;
    }

    &__actions {
        display: flex;
        justify-content: center;
        width: 100%;
    }

    &__upload-input {
        position: absolute;
        width: 0;
        height: 0;
        visibility: hidden;
    }

    &__btn {
        min-width: 172px;
        padding: 12px 16px;
        margin: 0 8px;
    }

    &__animated-content {
        position: relative;
        width: 370px;
        height: 213px;
    }

    &__default {
        z-index: 1;
    }

    &__default,
    &__in-progress {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
    }

    &__in-progress {
        top: 52px;
        opacity: 0;
    }

    &__loading-box {
        position: relative;
        margin: 0 auto 24px;
        width: 56px;
        height: 56px;
    }

    &__loading-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 30px;
        height: 30px;
        transform: translate(-50%, -50%);
    }
}

.fade-out-up {
    animation-name: fadeOutUp;
}

.fade-in-up {
    animation-name: fadeInUp;
}

.animated {
    animation-duration: 0.6s;
    animation-fill-mode: both;
}

@keyframes fadeOutUp {
    from {
        visibility: visible;
        opacity: 1;
    }

    to {
        opacity: 0;
        visibility: hidden;
        transform: translate3d(0, -20%, 0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        visibility: hidden;
        transform: translate3d(0, 20%, 0);
    }

    to {
        opacity: 1;
        visibility: visible;
        transform: translate3d(0, 0, 0);
    }
}
