@import '~assets/sass/variables';
@import '~assets/sass/animations';

.float-left {
    float: left;
}

.button-flex-layout {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.progress-bar {
    position: absolute;
    right: 24px;
    top: calc(50% - 20px);
    height: 40px;
    overflow: hidden;
    border-radius: 5px;

    &__content-wrapper {
        height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        padding: 4px;
        white-space: nowrap;

        .mat-icon {
            width: 20px;
            height: 20px;
        }
    }

    .mat-progress-bar {
        height: 40px;
        width: 207px;
        overflow: hidden;
        border-radius: 5px;
    }

    .progress-label {
        position: absolute;
        top: calc(50% - 7px);
        left: 45%;
        font-family: Arial, serif;
        font-weight: 700;
        size: 14px;
        color: white;
    }
}

button {
    min-width: 207px;
    padding: 8px 10px;
    overflow: hidden;

    &.avl-btn {
        &:disabled {
            opacity: 1;
        }
    }
}

:host ::ng-deep .mat-progress-spinner circle,
.mat-spinner circle {
    stroke: #ffffff;
}
