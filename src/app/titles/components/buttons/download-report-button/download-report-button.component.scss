@use 'assets/sass/variables' as *;

.report-button {
    position: relative;
    display: flex;
    gap: 12px;
    min-width: 262px;
    width: 100%;
    height: 100%;
    padding: 10px 50px 10px 16px;
    border: 1px solid #ececec;
    border-radius: 4px;
    text-align: left;
    background-color: transparent;
    cursor: pointer;
    transition: all 0.2s linear;

    &__icon {
        width: 40px;
        height: 40px;
    }

    &:hover,
    &:active {
        border-color: rgba($gray, 0.3);
        background-color: rgba($gray, 0.3);

        &:after {
            visibility: visible;
            opacity: 1;
        }
    }

    &:after,
    &:before {
        content: '';
        position: absolute;
        top: 16px;
        right: 16px;
        display: block;
        width: 24px;
        height: 24px;
        background-repeat: no-repeat;
        visibility: hidden;
        opacity: 0;
        transition: all 0.2s linear;
    }

    &:after {
        background-image: url('~assets/icons/icon_get_report.svg');
    }

    &:before {
        background-image: url('~assets/icons/icon_green_check.svg');
    }

    &.downloaded:not(:hover) {
        &:before {
            visibility: visible;
            opacity: 1;
        }
    }
}

.centered {
    &.report-button {
        padding-left: 34%;
    }
}

.title-format {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.title {
    margin-bottom: 2px;
    font-family: 'ArialBold', serif;
    color: #263238;
    font-size: 14px;
    line-height: 18px;
}

.format {
    font-size: 12px;
    line-height: 16px;
    color: rgba(#263238, 0.54);
}

.display-block {
    display: block;
}
