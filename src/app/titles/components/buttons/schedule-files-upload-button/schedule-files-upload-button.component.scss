@use 'assets/sass/variables' as *;

.progress-bar {
    position: absolute;
    right: 24px;
    top: calc(50% - 20px);
    height: 40px;
    overflow: hidden;
    border-radius: $primary-border-radius;

    &__content-wrapper {
        height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        padding: 4px;

        .mat-icon {
            width: 20px;
            height: 20px;
        }
    }

    .mat-progress-bar {
        height: 40px;
        width: 207px;
        overflow: hidden;
        border-radius: $primary-border-radius;
    }

    .progress-label {
        position: absolute;
        top: calc(50% - 7px);
        left: 45%;
        font-family: Arial, serif;
        font-weight: 700;
        color: white;
    }
}

button {
    min-width: 207px;
    padding: 8px 10px;
    overflow: hidden;

    &.avl-btn {
        &:disabled {
            opacity: 1;
        }
    }
}

.lift-up,
.lift-down {
    animation-duration: 1s;
    animation-fill-mode: both;
}

.lift-up {
    animation-name: liftUp;
}

.lift-down {
    animation-name: liftDown;
}

@keyframes liftUp {
    from {
        transform: translateY(0);
    }

    to {
        transform: translateY(-55px);
    }
}

@keyframes liftDown {
    from {
        transform: translateY(-55px);
    }

    to {
        transform: translateY(0);
    }
}
