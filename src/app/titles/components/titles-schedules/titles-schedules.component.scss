@use 'assets/sass/variables' as *;

:host {
    display: flex;
    flex-direction: column;
    height: calc(100% - 64px);
}

::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

::-webkit-scrollbar-track {
    background-color: $gray;
}

::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background-color: $dark-gray;
}

.schedules-btn {
    min-height: 60px;
    margin-top: 16px;
    margin-bottom: 20px;
    padding-top: 22px;
    padding-bottom: 22px;
}

.collection {
    &__container {
        flex: 1 100%;
        background: #fff;
        border-radius: 5px;

        h4 {
            text-align: center;
            margin-top: 15px;
            color: $dark-gray;
            opacity: 0.5;
            font-size: 12px;
            font-family: 'ArialBold';
            line-height: 16px;
            text-transform: uppercase;
        }

        .line {
            margin-top: 17px;
            height: 1px;
            background: $gray;
            opacity: 0.3;
        }

        avl-search {
            width: 208px;
        }
    }

    &__wrapper {
        padding: 25px 24px 7px 24px;

        ::ng-deep {
            .avl-search {
                height: 56px;
                position: relative;

                &__input {
                    margin-left: 6px;
                }

                mat-icon {
                    position: absolute;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: $blue;
                    height: 56px;
                    width: 59px;
                    right: 0;
                    border-radius: 0 4px 4px 0;
                    opacity: 1 !important;

                    svg {
                        height: 24px;
                        width: 24px;
                        fill: #fff;
                    }
                }
            }
        }
    }
}

.controls-container {
    display: flex;
    justify-content: space-between;

    button {
        &:first-of-type {
            margin-right: 8px;
            width: 24%;
        }

        &:last-of-type {
            width: 75%;
        }
    }
}
