import { Component, OnDestroy, OnInit } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { filter, finalize, take, takeUntil } from 'rxjs/operators';
import { SchedulesQuery } from '../../store';
import { FolderService, ReportGenerationHandlerService } from '../../services';
import { ISchedule, ScheduleActionEvent, ScheduleKey } from '../../types';
import { OnboardingManageService } from '../../../onboarding/services';
import { SchedulesTableService } from '../../services/schedules-table.service';
import { SchedulesService } from '../../store/schedules/schedules.service';
import { LinkedDocumentAnalysisService } from '../../services/linked-document-analysis.service';
import { ProfileService } from '@services';
import { ScheduleAction } from '../../enums';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';

@Component({
    selector: 'avl-titles-schedules',
    templateUrl: './titles-schedules.component.html',
    styleUrls: ['./titles-schedules.component.scss'],
})
export class TitlesSchedulesTableComponent implements OnInit, OnDestroy {
    public schedulesLoading$: Observable<boolean>;
    public folderId: string;
    public isAnyActionInProgress$: Observable<boolean>;
    public isNextButtonDisabledByOnboarding = false;
    public isOnboardingLoading = true;

    private readonly destroy$ = new Subject<void>();
    private searchQuery = '';

    constructor(
        private readonly schedulesQuery: SchedulesQuery,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly folderService: FolderService,
        private readonly schedulesService: SchedulesService,
        private readonly schedulesTableService: SchedulesTableService,
        private readonly handlerService: ReportGenerationHandlerService,
        private readonly onboarding: OnboardingManageService,
        private readonly linkedDocumentsAnalysisService: LinkedDocumentAnalysisService,
        private readonly profileService: ProfileService,
    ) {
    }

    public get currentSchedules(): ISchedule[] {
        return this.schedulesQuery.getAll()
            .filter((item: ISchedule) => {
                const query = this.searchQuery.toLocaleLowerCase();
                const isItemTypeIncludesQuery = (item?.type || '').toLocaleLowerCase().includes(query);
                const isItemDescriptionIncludesQuery = (item?.description || '').toLocaleLowerCase().includes(query);

                return isItemTypeIncludesQuery || isItemDescriptionIncludesQuery;
            });
    }

    public ngOnInit(): void {
        this.onboarding.isDisabledNextButton$
            .pipe(takeUntil(this.destroy$))
            .subscribe((isDisabled) => {
                this.isNextButtonDisabledByOnboarding = this.onboarding.isActive && isDisabled;
            });
        this.onboarding.showAddScheduleStep()
            .pipe(
                takeUntil(this.destroy$),
                finalize(() => this.isOnboardingLoading = false),
            )
            .subscribe((isBranchAvailable) => {
                if (isBranchAvailable && this.onboarding.isActive) {
                    this.onboarding.startAddingSchedule();
                }
            });
        this.profileService.isOneClickFiledCopiesEnabled$
            .pipe(
                takeUntil(this.destroy$),
                filter((isEnabled) => !isEnabled),
                take(1),
            )
            .subscribe(() => {
                this.schedulesService.purchaseFinishEvent$
                    .pipe(takeUntil(this.destroy$))
                    .subscribe(async (schedule) => {
                        if (schedule.key === ScheduleKey.linkedDocuments) {
                            const isPositive = await this.linkedDocumentsAnalysisService.tryToOpenFiledCopiesAnalysisDialog();
                            if (isPositive) {
                                const filedCopiesAnalysis = this.schedulesQuery.getEntity(ScheduleKey.linkedDocumentsAnalysis);
                                this.schedulesTableService.handleAction(filedCopiesAnalysis, ScheduleAction.startPurchase);
                                this.linkedDocumentsAnalysisService.emitAnalysisStartedEvent();
                            }
                        }
                    });
            });
        this.isAnyActionInProgress$ = this.schedulesQuery.selectIsAnyActionInProgress();
        this.schedulesLoading$ = this.schedulesQuery.selectLoading();
        this.schedulesQuery.selectLinkedDocuments()
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => this.schedulesService.recalculatePrice());

        this.folderId = this.projectDetailsQuery.projectId;
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    public onSearch(searchQuery: string): void {
        this.searchQuery = searchQuery;
    }

    public onSchedulesAction({ schedule, action }: ScheduleActionEvent): void {
        this.schedulesTableService.handleAction(schedule, action);
    }

    public onOpenGenerateReportDialog(): void {
        this.handlerService.openGenerateReportDialog();
    }
}
