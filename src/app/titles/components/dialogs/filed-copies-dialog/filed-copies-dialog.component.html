<div
    mat-dialog-title
    class="dialog-header"
>
    <h4 class="remove-margin-bottom title">FILED COPIES</h4>
    <button
        class="dialog__close"
        type="button"
        mat-dialog-close="close"
    >
        <mat-icon
            class="close-icon"
            svgIcon="close"
        ></mat-icon>
    </button>
    <div class="filters">
        <avl-filed-copies-filter
            [filtersOptions]="filtersOptions"
            (filterUpdated)="onFilterUpdated($event)"
        ></avl-filed-copies-filter>
        <avl-tab-like-selector
            class="availability-selector"
            [options]="availabilityOptions"
            [selectedOptionId]="selectedAvailabilityOptionId"
            (selected)="onAvailabilityFilterChanged($event)"
        ></avl-tab-like-selector>
    </div>
</div>
<mat-dialog-content>
    <div class="table-container">
        <div
            *ngIf="!(filteredDataSource$ | async).length"
            class="empty-disclaimer"
        >
            Nothing found
        </div>
        <table
            *ngIf="(filteredDataSource$ | async).length"
            mat-table
            class="collection__table mat-elevation-z8 field-copies-dialog-table"
            data-testid="filed-copies-table"
            [dataSource]="filteredDataSource$ | async"
            [trackBy]="trackById"
        >
            <!-- Parent Title Column -->
            <ng-container matColumnDef="reference">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    Parent Title
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    {{ element.reference }}
                </td>
            </ng-container>

            <!-- Type Column -->
            <ng-container matColumnDef="type">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    Type
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    {{ element.documentType }}
                </td>
            </ng-container>

            <!-- Filed Under Column -->
            <ng-container matColumnDef="filedUnder">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    Filed Under
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    {{ element.filedUnder }}
                </td>
            </ng-container>

            <!-- Location Column -->
            <ng-container matColumnDef="location">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    Location
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    {{ element.entryNumbers.join(', ') }}
                </td>
            </ng-container>

            <!-- Document Date Column -->
            <ng-container matColumnDef="documentDate">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    Document Date
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    {{ element.documentDate | date:'d MMMM y' }}
                </td>
            </ng-container>

            <!-- Document Date Column -->
            <ng-container matColumnDef="add">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    <label
                        *ngIf="(filteredDataSource$ | async).length"
                        class="collection__add-all-control"
                    >
                        <ng-container *ngIf="isAnyAvailableToPurchase$ | async">
                            <input
                                type="checkbox"
                                class="checkbox-input"
                                (change)="onSelectAllToggle()"
                                [checked]="isAllSelected$ | async"
                            >
                            <ng-container *ngIf="isAllSelected$ | async; else selectAllButton">
                                <mat-icon
                                    class="height-12px"
                                    svgIcon="close-red"
                                ></mat-icon>
                                <span class="checkbox-text pink-color">Remove</span>
                            </ng-container>
                            <ng-template #selectAllButton>
                                <mat-icon svgIcon="add-to-cart"></mat-icon>
                                <span class="checkbox-text">Add All</span>
                            </ng-template>
                        </ng-container>
                    </label>
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    <avl-filed-copy-button
                        [state]="extractButtonState(element)"
                        (click)="handleItemAction(element)"
                    ></avl-filed-copy-button>
                </td>
            </ng-container>

            <tr
                *matHeaderRowDef="displayedColumns"
                mat-header-row
            ></tr>
            <tr
                *matRowDef="let row; columns: displayedColumns;"
                mat-row
            ></tr>
        </table>
    </div>
</mat-dialog-content>
<div>
    <!--    <button-->
    <!--        class="avl-btn avl-btn&#45;&#45;blue avl-btn&#45;&#45;wide btn-border-n"-->
    <!--        type="button"-->
    <!--        [disabled]="!itemAdded"-->
    <!--        (click)="saveAddFileCopy()"-->
    <!--    >-->
    <!--        Add to project-->
    <!--        <span *ngIf="totalCost !== 0">-->
    <!--            for {{ costCurrency }}{{ totalCost }}-->
    <!--        </span>-->
    <!--    </button>-->
    <button
        class="avl-btn avl-btn--blue avl-btn--wide btn-border-n"
        type="button"
        [disabled]="(isAtLeastOneSelected$ | async) !== true"
        (click)="saveAddFileCopy()"
    >
        SELECT
    </button>
</div>
