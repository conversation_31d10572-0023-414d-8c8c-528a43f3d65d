@use 'assets/sass/variables' as *;

:host {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.dialog-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px 34px 5px 24px;
}

.filters {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 10px;
}

.mat-dialog-title {
    .filters {
        text-transform: initial;
    }
}

.remove-margin-bottom {
    margin-bottom: 0;
}

.height-12px {
    height: 12px;
}

.pink-color {
    color: $pink !important;
}

.field-copies-dialog-table {
    width: 100%;
    box-shadow: none;

    th.mat-header-cell:last-of-type {
        width: 117px;
        text-align: right;
    }

    .mat-column-select {
        width: 117px;
        text-align: right;
    }

    .mat-header-cell {
        text-transform: uppercase;
    }
}

.mat-dialog-content {
    max-height: 78vh;
}

.btn-border-n {
    border-radius: 0;
    margin-top: 16px;
    padding-top: 22px;
    padding-bottom: 22px;
}

.add-container {
    display: flex;

    .text {
        font-size: 14px;
        margin-left: 5px;
    }
}

.mat-checkbox-label {
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    line-height: 18px;
    color: #333333;
}

.mat-ripple-element {
    background: #999 !important;
}

.header-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    padding: 0 20px 10px 0;
}

.upload-button {
    padding: 10px;
    font-size: 14px;
    font-family: Arial, serif;
}

.upload-button-icon {
    cursor: pointer;
    padding-top: 8px;
    margin-right: 85px;
}

.upload-button-icon:hover {
    opacity: 0.8;
}

.file-chooser-button {
    margin: 0 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.hidden {
    display: none;
}

.uploaded-document-column__select {
    width: 100%;
}

.uploaded-docs-badge-container {
    position: relative;
}

.upload-button-spinner-container {
    position: relative;
}

.upload-button-disabled {
    opacity: 0.8;
}

.upload-button-spinner {
    position: absolute;
    inset: 0;

    top: 9px;
    left: -58px;
}

.uploaded-docs-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    margin-left: 5px;
    padding: 0 4px;
    min-width: 16px;
    height: 16px;
    font-size: 10px;
    line-height: 16px;
    background-color: $pink;
    color: white;
    border-radius: 24px;
}

.empty-disclaimer {
    display: flex;
    justify-content: center;
    padding-top: 50px;
    color: #999;
    font-size: 14px;
}
