@import '~assets/sass/variables';

.download-report {
    .mat-dialog-title {
        margin-bottom: 0;
    }

    .mat-dialog-content {
        margin: 0 0 20px;
        padding: 0;
        display: flex;
        flex-direction: column;
    }

    &__subtitle {
        margin-top: 8px;
        font-size: 12px;
        line-height: 16px;
        text-decoration: initial;
        color: $dark-gray;
        text-transform: initial;
    }

    &__section {
        display: flex;
        gap: 8px;
        padding: 16px 24px 16px 48px;
        flex-direction: column;
    }

    &__section-title {
        color: $dark-gray;
        font-family: Roboto, sans-serif;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 1px;
        text-transform: uppercase;
        text-align: start;
    }

    &__items {
        display: flex;
        max-width: 590px;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 18px;
        padding-right: 16px;
    }

    &__item {
        width: 100%;
        flex-basis: calc(50% - 9px);

        &_stretch {
            flex-basis: auto;
        }
    }
}

.border-bottom {
    border-bottom: 1px solid #eaeaea;
}

.individual-reports-container {
    overflow-y: auto;
    max-height: 400px;
}

.individual-reports-container::-webkit-scrollbar {
    width: 4px;
}

.individual-reports-container::-webkit-scrollbar-track {
    background-color: rgb(#d6d7dd, 0.2);
    width: 4px;
    border-radius: 2px;
}

.individual-reports-container::-webkit-scrollbar-thumb {
    background-color: #d6d7dd;
    border-radius: 2px;
}

.dialog-content {
    min-height: 330px;
    justify-content: center;
}
