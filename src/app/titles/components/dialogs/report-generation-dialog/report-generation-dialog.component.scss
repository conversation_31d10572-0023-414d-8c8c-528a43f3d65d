@use 'assets/sass/variables' as *;

.report-generation {
    &__steps {
        padding: 10px 40px;
    }

    &__step-item {
        display: flex;
        align-items: center;
        margin: 24px 0;

        &.animated {
            .checkmark__circle {
                stroke: $green;
                animation: stroke 0.5s cubic-bezier(0.65, 0, 0.45, 1) forwards;
            }

            .checkmark {
                animation:
                    fill 0.3s ease-in-out 0.3s forwards,
                    scale 0.2s ease-in-out 0.6s both;
            }

            .checkmark__check {
                animation: stroke 0.2s cubic-bezier(0.65, 0, 0.45, 1) 0.5s forwards;
            }
        }
    }

    &__step-icon {
        width: 32px;
        height: 32px;
    }

    &__step-title {
        margin-left: 17px;
    }
}

.checkmark__circle {
    stroke-dasharray: 166;
    stroke-dashoffset: 166;
    stroke-width: 2;
    stroke-miterlimit: 10;
    stroke: $gray;
    fill: none;
}

.checkmark {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: block;
    stroke-width: 2;
    stroke: #fff;
    stroke-miterlimit: 10;
    box-shadow: inset 0 0 0 $green;
}

.checkmark__check {
    transform-origin: 50% 50%;
    stroke-dasharray: 48;
    stroke-dashoffset: 48;

    &.animated {
        animation: stroke 0.2s cubic-bezier(0.65, 0, 0.45, 1) 0.7s forwards;
    }
}

@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .report-generation__step-item {
        .checkmark__circle {
            stroke-dasharray: none;
        }

        .checkmark__check {
            stroke-dasharray: none;
        }
    }
}

@keyframes stroke {
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes scale {
    0%,
    100% {
        transform: none;
    }

    50% {
        transform: scale3d(1.1, 1.1, 1);
    }
}

@keyframes fill {
    100% {
        box-shadow: inset 0px 0px 0px 30px $green;
    }
}
