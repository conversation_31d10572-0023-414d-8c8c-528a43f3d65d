import { Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';

import { map, Observable, Subscription } from 'rxjs';
import { IFileUploaderEvents } from '../../../../blocks/file-uploader';
import { fileUploaderOptions, SOMETHING_WENT_WRONG_DURING_FILES_UPLOADING } from '@constants';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { UploadDropAreaComponent } from '@shared/components/upload-drop-area/upload-drop-area.component';
import { SearchFile } from '../../../types/search-file.type';
import { SearchFilesQuery, SearchFilesService } from '../../../store';
import { SearchFilesApi } from '../../../api/search-files.api';
import { IFailedDocument } from '@core/types';
import { SearchType, UploadDialogsStatus } from '../../../enums';
import { ProjectDetailsQuery } from '../../../../project-details/stores/project-details/project-details.query';

export type UploadSearchesDialogComponentParams = {
    type: SearchType;
};

@Component({
    selector: 'avl-upload-searches-dialog',
    templateUrl: './upload-searches-dialog.component.html',
    styleUrls: ['./upload-searches-dialog.component.scss'],
})
export class UploadSearchesDialogComponent implements OnInit, OnDestroy {
    public readonly fileUploaderOptions = fileUploaderOptions;
    public readonly isLocalSearch: boolean;
    public readonly defaultDocType: string = null;

    public folderId = '';
    public isLoading$: Observable<boolean>;
    public searchFiles$: Observable<SearchFile[]>;
    public failedFiles$: Observable<IFailedDocument[]>;
    public fileWaitsUploadingCount$: Observable<number>;

    @ViewChild(UploadDropAreaComponent, { static: true })
    private readonly dropAreaComponent?: UploadDropAreaComponent;

    private readonly subscriptions = new Subscription();

    constructor(
        private readonly searchesService: SearchFilesService,
        private readonly searchFilesQuery: SearchFilesQuery,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly searchFilesApi: SearchFilesApi,
        private readonly dialogRef: MatDialogRef<UploadSearchesDialogComponent>,
        @Inject(MAT_DIALOG_DATA)
        public readonly params: UploadSearchesDialogComponentParams,
    ) {
        this.isLocalSearch = params.type === SearchType.local;
        this.defaultDocType = this.isLocalSearch ? SearchType.local : null;
    }

    public ngOnInit(): void {
        this.folderId = this.projectDetailsQuery.projectId;
        this.searchFiles$ = this.searchFilesQuery.selectSortedFiles$();
        this.isLoading$ = this.searchFilesQuery.selectLoading();
        this.failedFiles$ = this.searchFilesQuery.selectFailedFiles$()
            .pipe(map(this.mapErrors));
        this.fileWaitsUploadingCount$ = this.searchFilesQuery.fileWaitsUploadingCount$;
        this.searchesService.loadSearchFiles(this.isLocalSearch ? SearchType.local : SearchType.other);
    }

    public ngOnDestroy(): void {
        this.subscriptions.unsubscribe();
        this.searchesService.stopFolderStatusPoll();
        this.searchesService.clearLoadedSearches();
    }

    public onNextButtonClick(): void {
        this.dialogRef.close(UploadDialogsStatus.startProcessing);
    }

    public onBeforeUpload(amount: number): void {
        if (!amount) {
            return;
        }

        this.startFilesUploading();
    }

    public onFailedDocumentRemove(id: string): void {
        this.searchesService.removeOrCancelFile(id);
        this.searchesService.removeErrorFileMessages(id);
    }

    public onFileUploadStarted(event: IFileUploaderEvents): void {
        const temporaryFileId = event.file.uuid;
        this.searchesService.addTemporaryFile(temporaryFileId, this.isLocalSearch ? SearchType.local : null);
    }

    public onFileUploadingFailed(event?: IFileUploaderEvents): void {
        const error = event ? event.error : Error();
        const temporaryFileId = event ? event.file.uuid : '';
        const errorData = { ...SOMETHING_WENT_WRONG_DURING_FILES_UPLOADING };

        if (error.status === 413) {
            const fileName = event.file.fileAsObject.name || 'File';
            errorData.title = 'Too Large File';
            errorData.message = `${fileName} is too large to be a valid document.`;
        }

        this.searchesService.openAlertDialog(errorData);
        this.searchesService.removeOrCancelFile(temporaryFileId);
    }

    public onFileUploadingCanceled(event: IFileUploaderEvents): void {
        const temporaryFileId = event.file.uuid;
        this.searchesService.removeOrCancelFile(temporaryFileId);
    }

    public cancelUploading(fileId: string): void {
        this.dropAreaComponent.cancelUpload(fileId);
    }

    public onFileUploadingSucceeded(event: IFileUploaderEvents): void {
        const temporaryFileId = event.file.uuid;
        const documentId = event.response.body[0];

        this.searchesService.saveUploadedFile(temporaryFileId, documentId);
    }

    private startFilesUploading(): void {
        const folderId = this.projectDetailsQuery.projectId;
        const documentType = this.isLocalSearch ? SearchType.local : null;
        const url = this.searchFilesApi.createUploadingUrl(folderId, documentType);

        this.dropAreaComponent.uploadAll(url);
        this.searchesService.startUploading();
    }

    private mapErrors(failedFiles: SearchFile[]): IFailedDocument[] {
        return failedFiles.map((file) => ({
            id: file.id,
            message: file.messages?.[0]?.message || '',
            fileName: file.fileName,
        }));
    }
}

