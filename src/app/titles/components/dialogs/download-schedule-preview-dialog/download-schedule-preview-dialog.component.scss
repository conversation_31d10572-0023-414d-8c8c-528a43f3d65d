@use 'assets/sass/variables' as *;

:host {
    display: block;
    height: 90vh;
    width: 80vw;

    ::-webkit-scrollbar {
        width: 5px;
    }

    ::-webkit-scrollbar-track {
        background-color: $gray;
    }

    ::-webkit-scrollbar-thumb {
        border-radius: 5px;
        background-color: $dark-gray;
    }

    .mat-dialog-title {
        padding: 16px;
        opacity: 0.5;

        // text-transform: uppercase
        font-size: 12px;
        font-family: 'Arial';
        font-weight: 700;
        line-height: 20px;
    }

    .schedule-preview__content {
        width: 100%;
        max-height: calc(90vh - 53px);
        background: $mid-gray;

        img {
            width: 90%;
        }
    }
}
