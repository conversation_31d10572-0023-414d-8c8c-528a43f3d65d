@import '~assets/sass/variables';

:host {
    display: flex;
    xheight: 90vh;
    xwidth: 80vw;
}

:host ::ng-deep .imanage-upload-dialog {
    width: 100%;
    xmax-width: 1024px;
}

:host .mat-dialog-container {
    position: relative;
    padding: 0 !important;
    overflow: hidden;
    xwidth: 1024px;
}

.margin-auto {
    margin: auto;
}

.iframe {
    height: 75vh;
    width: 75vw;
}

.wrapper {
    width: 100%;
    height: 100%;
    position: relative;

    .overlay {
        position: absolute;
        z-index: 1002;
        background-color: rgba(255, 255, 255, 0.5);
        width: 100%;
        height: 100%;

        .spinner-wrapper {
            display: flex;
            justify-content: center;
            justify-items: center;
            width: 100%;
            height: 100%;
        }
    }
}
