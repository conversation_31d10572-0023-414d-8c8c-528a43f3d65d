@use 'assets/sass/variables' as *;
@use 'assets/sass/mixins' as *;

.land-registry-dialog {
    width: 100%;

    &__title {
        text-transform: uppercase;
        font-family: Arial, 'sans-serif';
        font-size: 14px;
        font-weight: 700;
        line-height: 18px;
        color: #96969e;
    }

    &__header {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 56px;
        padding: 12px 0;
        border-bottom: 1px solid #eaeaea;
    }

    .land-registry-search {
        display: block;
        margin: 24px;
    }

    &__map-results {
        width: 100%;
        height: 70vh;
        max-height: 100%;
        background-color: #c5cdd0;
    }

    &__results {
        height: 70vh;
        display: flex;
        flex-direction: column;

        @include maxH(900px) {
            height: 60vh;
        }
    }

    &__results-container {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .mat-dialog-container {
        position: relative;
        padding: 0;
        overflow: hidden;
    }

    .mat-dialog-title {
        text-align: center;
    }

    &__error-container {
        padding: 0 24px;

        avl-multiple-error-alert {
            display: block;
            margin: 8px 0;
        }
    }

    avl-land-registry-results-table {
        position: relative;
        display: flex;
        flex-direction: column;
        flex: 1;
        margin-top: 8px;
        overflow: auto;
    }

    &__results-btn-container {
        padding: 0 20px;
    }

    &__download-button {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 0 4px;
        font-size: 14px;
        line-height: 28px;
        color: $dark-gray;
        background-color: transparent;
        border: none;
        border-radius: 4px;
        vertical-align: middle;
        cursor: pointer;
    }

    &__download-icon {
        width: 20px;
        height: 20px;
    }
}

.registry-selector {
    width: 262px;
    height: 32px;

    & .mat-select-trigger {
        height: 32px;
        line-height: 32px;
    }

    &__option {
        display: flex;
        align-items: center;
    }

    &__text-container {
        display: flex;
        width: 100%;
        justify-content: center;
        text-align: left;
    }
}

.background-white-circle {
    --background-color: white;
    display: flex;
    justify-content: center;

    & .mat-icon {
        width: 18px;
        height: 18px;
    }
}
