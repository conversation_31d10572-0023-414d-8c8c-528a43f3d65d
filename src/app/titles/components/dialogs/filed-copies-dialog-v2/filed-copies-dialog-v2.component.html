<div
    mat-dialog-title
    class="dialog-header"
>
    <div class="header-and-tour-button">
        <h4 class="remove-margin-bottom title">FILED COPIES</h4>
        <mat-icon
            *ngIf="isUploadEnabled$ | async"
            class="reset-filed-copies-tour"
            svgIcon="exclamation-circle"
            matTooltipClass="mat-tooltip tooltip__min-w-200 tooltip__p8 tooltip__rounded4 tooltip__text14px
                tooltip__gray-blue-bg"
            matTooltipPosition="right"
            matTooltip="Restart the UI tour."
            (click)="restartFiledCopiesTour()"
        ></mat-icon>
    </div>
    <button
        class="dialog__close"
        type="button"
        [disabled]="isCloseButtonDisabled"
        (click)="close()"
    >
        <mat-icon
            class="close-icon"
            svgIcon="close"
        ></mat-icon>
    </button>
    <div
        class="filters"
        ui-tour-element="filed-copies-filters"
    >
        <avl-filed-copies-filter
            [filtersOptions]="filtersOptions"
            (filterUpdated)="onFilterUpdated($event)"
        ></avl-filed-copies-filter>
        <label
            for="group-duplicates"
            style="white-space: nowrap; gap: 5px; display: flex; align-items: center; flex-wrap: nowrap;"
        >
            <avl-switch-toggle
                buttonId="group-duplicates"
                [isActivated]="isDuplicatesGrouped$ | async"
                (toggleChanged)="onGroupToggleChanged($event)"
            ></avl-switch-toggle>
            Group Duplicates
        </label>
        <avl-tab-like-selector
            class="availability-selector"
            [options]="availabilityOptions"
            [selectedOptionId]="selectedAvailabilityOptionId"
            (selected)="onAvailabilityFilterChanged($event)"
        ></avl-tab-like-selector>
    </div>
</div>
<mat-dialog-content
    #dialogContentRef
    class="scroll-container--thin-scroll"
    avlTrackScroll
    [class.scroll-right-padding]="!hasScroll"
    (hasScrollChange)="onScrollDetected($event)"
>
    <div
        *ngIf="purchaseAllErrors().length"
        class="purchase-all-errors"
    >
        <avl-expandable-errors-alert
            [isBright]="true"
            [errors]="purchaseAllErrors()"
        ></avl-expandable-errors-alert>
    </div>
    <div class="table-container">
        <div
            *ngIf="!(filteredDataSource$ | async).ungrouped.length && !(filteredDataSource$ | async).grouped.length"
            class="empty-disclaimer"
        >
            Nothing found. Try to change filter values
        </div>

        <!-- Grouped Items with Color Coding -->
        <div *ngFor="let group of (filteredDataSource$ | async).grouped; let groupIndex = index" class="grouped-table-container">
            <table
                mat-table
                multiTemplateDataRows
                class="collection__table mat-elevation-z8 field-copies-dialog-table grouped-table"
                [style.cursor]="!!draggedRowFiledCopyId ? 'default' : 'initial'"
                [style.border-left]="'4px solid ' + getGroupColor(groupIndex)"
                [dataSource]="group"
                [trackBy]="trackById"
            >

        <!-- Ungrouped Items -->
        <table
            *ngIf="(filteredDataSource$ | async).ungrouped.length"
            mat-table
            multiTemplateDataRows
            class="collection__table mat-elevation-z8 field-copies-dialog-table"
            data-testid="filed-copies-table"
            [style.cursor]="!!draggedRowFiledCopyId ? 'default' : 'initial'"
            [dataSource]="(filteredDataSource$ | async).ungrouped"
            [trackBy]="trackById"
        >
            <!-- Parent Title Column -->
            <ng-container matColumnDef="reference">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    Parent Title
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    {{ element.reference }}
                </td>
            </ng-container>

            <!-- Type Column -->
            <ng-container matColumnDef="type">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    Type
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    {{ element.documentType }}
                </td>
            </ng-container>

            <!-- Filed Under Column -->
            <ng-container matColumnDef="filedUnder">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    Filed Under
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    {{ element.filedUnder }}
                </td>
            </ng-container>

            <!-- Location Column -->
            <ng-container matColumnDef="location">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    Location
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    {{ element.entryNumbers.join(', ') }}
                </td>
            </ng-container>

            <!-- Document Date Column -->
            <ng-container matColumnDef="documentDate">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    Document Date
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    {{ element.documentDate | date:'d MMMM y' }}
                </td>
            </ng-container>

            <!-- Document Upload Column -->
            <ng-container
                matColumnDef="upload"
            >
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    Upload / View
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    <ng-container
                        *ngIf="draggedRowFiledCopyId === element.id && isUploadAvailable(element); else uploadButton"
                    >
                        <div
                            class="drop__status"
                            @fadeIn
                        >
                            <svg
                                class="drop__icon-status"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 -960 960 960"
                                fill="#b4b4b4"
                            >
                                <path
                                    d="M440-320v-326L336-542l-56-58 200-200 200 200-56 58-104-104v326h-80ZM240-160q-33
                                0-56.5-23.5T160-240v-120h80v120h480v-120h80v120q0 33-23.5 56.5T720-160H240Z"
                                />
                            </svg>
                        </div>
                    </ng-container>

                    <ng-template #uploadButton>
                        <input
                            #fileSelector
                            class="hidden"
                            avlFilePicker
                            type="file"
                            accept="application/pdf"
                            [uploader]="fileUploaders[element.id]"
                        >
                        <div @fadeIn>
                            <avl-filed-copy-upload-button
                                ui-tour-element="filed-copies-upload-button"
                                primaryColor="white"
                                actionBgColor="#748ece"
                                successBgColor="#65C079"
                                errorBgColor="#f6616e"
                                loadingLabel="Uploading..."
                                matTooltipClass="mat-tooltip__bright-without-opacity tooltip__max-w400"
                                matTooltipPosition="left"
                                [isDeleteButtonVisible]="!!element.file && element.isFiledCopyError && !element.isPurchased"
                                [disabled]="isUploadButtonDisabled(element)"
                                [matTooltipDisabled]="!element.file && !element.isFiledCopyError"
                                [matTooltip]="element.isFiledCopyError && element.errorMessage || element.file?.fileName"
                                [state]="uploadButtonState(element)"
                                (uploaded)="onUploadDocument(element, fileSelector)"
                                (opened)="openDocumentInNewTab(element)"
                                (removed)="onRemoveUploadedDocument(element)"
                            ></avl-filed-copy-upload-button>
                        </div>
                    </ng-template>
                </td>
            </ng-container>

            <!-- Document Purchase Column -->
            <ng-container matColumnDef="purchase">
                <th
                    *matHeaderCellDef
                    mat-header-cell
                >
                    <ng-container *ngIf="isAvailableItemExist$ | async; else unavailableButton">
                        <app-animated-button-five-states
                            ui-tour-element="filed-copies-purchase-all-button"
                            primaryColor="white"
                            idleBgColor="#748ece"
                            loadingBgColor="#b4b4b4"
                            successBgColor="#65C079"
                            errorBgColor="#f6616e"
                            loadingText="Purchasing..."
                            errorText="Error"
                            successText="All Purchased"
                            [disabled]="isPurchaseAllButtonDisabled()"
                            [state]="purchaseAllButtonState()"
                            [idleText]="'Purchase All for ' + (totalCost$ | async)"
                            (countdown)="onClickPurchaseAll()"
                        ></app-animated-button-five-states>
                    </ng-container>

                    <ng-template #unavailableButton>
                        <button
                            [disabled]="true"
                            class="unavailable-btn"
                        >
                            Nothing to Purchase
                        </button>
                    </ng-template>
                </th>
                <td
                    *matCellDef="let element"
                    mat-cell
                >
                    <ng-container *ngIf="element.isAvailable || element.file; else unavailableButton">
                        <app-animated-button-five-states
                            ui-tour-element="filed-copies-purchase-button"
                            primaryColor="white"
                            idleBgColor="#748ece"
                            undoBgColor="#d69835"
                            loadingBgColor="#b4b4b4"
                            successBgColor="#65C079"
                            errorBgColor="#f6616e"
                            loadingText="Purchasing..."
                            errorText="Error"
                            undoText="Undo"
                            [disabled]="isPurchaseButtonDisabled(element)"
                            [successText]="element.file && !element.isPurchased ? 'Uploaded' : 'Purchased'"
                            [cancelTimeoutMs]="3000"
                            [state]="purchaseButtonState(element)"
                            [idleText]="'Purchase for ' + itemPrice"
                            (cancel)="onPurchasingCanceled(element)"
                            (action)="purchaseOne(element)"
                            (countdown)="onPurchasingInited(element)"
                        ></app-animated-button-five-states>
                    </ng-container>
                    <ng-template #unavailableButton>
                        <button
                            [disabled]="true"
                            class="unavailable-btn"
                        >
                            Unavailable
                        </button>
                    </ng-template>
                </td>
            </ng-container>

            <ng-container matColumnDef="expandedDetail">
                <td
                    mat-cell
                    class="expanded"
                    *matCellDef="let element"
                    [attr.colspan]="displayedColumns.length"
                >
                    <avl-expandable-errors-alert
                        [id]="'error-alert-' + convertToValidId(element.id)"
                        [isBright]="true"
                        [errors]="itemErrors(element)"
                    ></avl-expandable-errors-alert>
                </td>
            </ng-container>

            <tr
                *matHeaderRowDef="displayedColumns"
                mat-header-row
            ></tr>
            <tr
                *matRowDef="let row; columns: displayedColumns;"
                mat-row
                [class.file-over]="draggedRowFiledCopyId === row.id && isUploadAvailable(row)"
                [class.upload-blocked]="!!draggedRowFiledCopyId && !isUploadAvailable(row)"
                avlFileDrop
                [uploader]="fileUploaders[row.id]"
                (dragenter)="markIsDraggingOverRow(row)"
                (dragleave)="onDragLeave()"
                (fileDrop)="onFileDrop(row)"
            ></tr>
            <tr
                mat-row
                *matRowDef="let row; columns: ['expandedDetail']"
                [ngClass]="{ error: true, visible: !!itemErrors(row).length }"
            ></tr>
            </table>
        </div>

        </table>
    </div>
</mat-dialog-content>
<div>
    <button
        class="avl-btn avl-btn--blue avl-btn--wide btn-border-n"
        type="button"
        [disabled]="isCloseButtonDisabled"
        (click)="close()"
    >
        CLOSE
    </button>
</div>
