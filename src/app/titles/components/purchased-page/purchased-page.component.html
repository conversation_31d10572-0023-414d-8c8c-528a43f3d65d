<div class="purchase-page">
    <avl-search-bar
        placeholder="Search your purchased files"
        (searchChanged)="onSearch($event)"
    ></avl-search-bar>
    <div class="collection__container table">
        <avl-purchased-files-table
            [loading]="loading$ | async"
            [files]="purchaseFiles$ | async"
            [pagination]="pagination"
            [resetSelection$]="resetSelection$"
            (pageChanged)="onPageChanged($event)"
            (selectedFiles)="onSelectedFiles($event)"
        ></avl-purchased-files-table>
    </div>
    <avl-purchase-download-button
        [counter]="(filesForDownload$ | async).length"
        [downloadAction]="downloadAction$"
        (filesDownloaded)="downloadFiles()"
    ></avl-purchase-download-button>
</div>
