@use 'assets/sass/variables' as *;
@use 'assets/sass/mixins' as *;

.lr-result-table {
    td.mat-cell:first-of-type,
    td.mat-footer-cell:first-of-type,
    th.mat-header-cell:first-of-type {
        padding-left: 24px;
    }

    td.mat-cell:last-of-type,
    td.mat-footer-cell:last-of-type,
    th.mat-header-cell:last-of-type {
        padding-right: 24px;
    }

    .mat-column-select {
        width: 117px;
        text-align: right;
    }
}

::ng-deep .mat-column {
    &-reference {
        min-width: 106px;
    }

    &-type {
        min-width: 75px;
    }

    &-address {
        min-width: 160px;
    }

    &-what3words {
        min-width: 110px;
    }

    &-proprietors {
        min-width: 160px;
    }

    &-rightType {
        min-width: 115px;
    }

    &-companyNumbers {
        min-width: 118px;
    }

    &-select {
        min-width: 128px;
    }
}

.text-truncate {
    @include multi-line-ellipsis(3);
}

.right-description-text {
    position: relative;
    padding-right: 25px;
}

.show-more-button {
    position: absolute;
    margin: auto;
    right: 0;
    top: 0;
    bottom: 0;

    width: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.show-more-icon {
    position: absolute;
    height: 5px;

    transition: transform 200ms ease-in-out;
    transform-origin: center center;

    &--spin-up {
        transform: rotate(180deg);
    }
}

.right-description-row {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px 0;

    color: #96969e;
    font-family: Arial, sans-serif;

    &__title {
        font-size: 14px;
        font-weight: 700;
        line-height: 18px;
        text-transform: uppercase;
    }

    &__content {
        font-size: 12px;
        font-weight: 400;
        line-height: 14px;
    }
}

.details-expandable-row {
    height: auto;

    td {
        height: 0;
        padding: 0;
        border: none;
    }
}
