import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { fadeRowAnimation } from '../../../core/animations/fade.animation';
import { BehaviorSubject, EMPTY, iif, Observable, Subject } from 'rxjs';
import { mergeMap, takeUntil, tap } from 'rxjs/operators';
import { NotifyCountersQuery, NotifyCountersState } from '../../store';
import { resetStores } from '@datorama/akita';
import { DocumentsService, NotifyCountersService } from '../../services';
import { ConfirmDialogComponent } from '@shared/components/dialogs/confirm-dialog/confirm-dialog.component';
import { CREATE_NEW_FOLDER_CONFIRM_DATA, SOMETHING_GONE_WRONG, TUTORIAL_VIEW_CONFIRM_DATA } from '@constants';
import { OnboardingManageService } from '../../../onboarding/services';
import { AlertDialogComponent } from '@shared/components/dialogs/alert-dialog/alert-dialog.component';
import { Logo } from '@core/types';
import { LoggerService, ProfileService } from '@services';
import { ProjectScopedStorageService } from '../../../core/services/project-scoped-storage.service';
import { TourControllerService } from '../../modules/sam-tour/services/tour-controller.service';
import { ProjectDetails } from '../../../project-details/types/project-details.type';
import { titleProjectDetailsOptions } from '../../constants/title-project-details-dialog-options.constant';
import { ProjectDetailsDialogService } from '../../../project-details/services/project-details-dialog.service';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';

@Component({
    selector: 'avl-title-registers',
    templateUrl: './title-registers.component.html',
    styleUrls: ['./title-registers.component.scss'],
    animations: [fadeRowAnimation],
})
export class TitleRegistersComponent implements OnInit, OnDestroy {
    public readonly titleLogo: Logo = {
        defaultLogo: true,
        icon: 'assets/images/avail-titles-logo.svg',
        iconWidth: 121,
        iconHeight: 20,
    };

    public folderIsNotCreated$: Observable<boolean>;
    public projectDetails$: Observable<ProjectDetails>;
    public counters$: Observable<NotifyCountersState>;
    public isOnboardingActive$: Observable<boolean>;
    private readonly destroy$ = new Subject<void>();

    constructor(
        private readonly countersQuery: NotifyCountersQuery,
        private readonly documentsService: DocumentsService,
        private readonly notifyCountersService: NotifyCountersService,
        private readonly profileService: ProfileService,
        private readonly onboarding: OnboardingManageService,
        private readonly dialog: MatDialog,
        private readonly router: Router,
        private readonly projectScopedStorage: ProjectScopedStorageService,
        private readonly samUiTour: TourControllerService,
        private readonly projectDetailsDialogService: ProjectDetailsDialogService,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly log: LoggerService,
    ) {
    }

    public ngOnInit(): void {
        this.projectDetails$ = this.projectDetailsQuery.selectDetails();
        this.folderIsNotCreated$ = this.projectDetailsQuery.select((state) => !state.id);
        this.counters$ = this.countersQuery.select();
        this.isOnboardingActive$ = this.onboarding.isOnboardingActive();
        this.countersUpdate();
        this.profileService.loadConfig();
        this.projectDetailsQuery.selectProjectId()
            .pipe(takeUntil(this.destroy$))
            .subscribe((id) => this.projectScopedStorage.updateProjectId(id));
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    public isHmlrEnabled$(): BehaviorSubject<boolean> {
        return this.profileService.isHmlrEnabled$;
    }

    public onNewProjectCreated(): void {
        const dialogRef = this.dialog.open(ConfirmDialogComponent, {
            panelClass: 'confirm-dialog',
            data: CREATE_NEW_FOLDER_CONFIRM_DATA,
        });

        dialogRef.afterClosed().subscribe((isConfirm) => {
            if (!isConfirm) {
                return;
            }
            this.resetViewState();
        });
    }

    public onOnboardingShowed(): void {
        this.samUiTour.reset();
        const dialogRef = this.dialog.open(ConfirmDialogComponent, {
            panelClass: 'confirm-dialog',
            data: TUTORIAL_VIEW_CONFIRM_DATA,
        });

        dialogRef.afterClosed()
            .pipe(
                tap((confirmationResult) => confirmationResult && this.resetViewState()),
                mergeMap((confirmationResult: boolean) => {
                    return iif(() => confirmationResult, this.onboarding.makeOnboardingVisible(), EMPTY);
                }),
            )
            .subscribe(() => this.onboarding.isShowReport = true);
    }

    public onOpenedStart(): void {
        this.countersUpdate();
        this.onboarding.showMainMenuStep().subscribe();
    }

    public onProjectDetailsOpen(): void {
        const options = {
            ...titleProjectDetailsOptions,
            disableClose: this.onboarding.isActive,
            data: this.projectDetailsQuery.getDetails(),
        };
        this.projectDetailsDialogService.show(options)
            .catch((error) => {
                this.log.error(error);

                if (error?.status !== 423) {
                    this.openAlertDialog(SOMETHING_GONE_WRONG);
                }
            });
    }

    private resetViewState(): void {
        resetStores({ exclude: ['notify-counters'] });
        this.documentsService.resetLoading();
        this.router.navigate(['title']);
    }

    private openAlertDialog(errorData?: { title: string; message: string }): void {
        this.dialog.open(AlertDialogComponent, {
            panelClass: 'report-dialog',
            width: '400px',
            data: errorData,
        });
    }

    private countersUpdate(): void {
        this.notifyCountersService.getCounters().subscribe();
    }
}
