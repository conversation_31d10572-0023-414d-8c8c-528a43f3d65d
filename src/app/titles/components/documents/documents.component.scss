@use 'assets/sass/variables' as *;
@use 'assets/sass/mixins' as *;
@use 'assets/sass/animations' as *;

$loading-placeholder-color: #ececec;
$row-height: 47px;

:host {
    display: block;
    border-radius: 5px;
    position: relative;
    overflow: hidden;
}

.documents {
    &__container {
        height: 100%;
        background-color: #fff;
        border-radius: 5px;
        overflow: auto;

        &::-webkit-scrollbar {
            width: 5px;
        }

        &::-webkit-scrollbar-track {
            background-color: $gray;
        }

        &::-webkit-scrollbar-thumb {
            border-radius: 5px;
            background-color: $dark-gray;
        }
    }

    &__table {
        position: relative;
        width: 100%;
        border-spacing: 0;
        z-index: 10;
    }

    &__empty-list {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        pointer-events: none;
    }

    &__empty-row {
        height: $row-height;
        border-bottom: 1px solid rgba(204, 204, 204, 0.3);
        pointer-events: none;
    }

    &__row {
        font-size: 12px;
        color: $black;
        background-color: #fff;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: #f7f7f7;

            .document-icon {
                opacity: 0;
                visibility: hidden;
            }

            .download-icon {
                opacity: 1;
                visibility: visible;
            }
        }

        &_dragdrop-off {
            .documents__col:first-of-type {
                padding-left: 35px;
            }

            .documents__col.type {
                min-width: 80px;
            }
        }

        .documents__col--head:hover {
            background-color: #f7f7f7;
        }

        .document-icon,
        .download-icon {
            position: absolute;
            top: -5px;
            left: 0;
            transition: all 0.2s ease;
            color: $dark-gray;
        }

        .document-icon {
            opacity: 1;
            visibility: visible;
        }

        .download-icon {
            opacity: 0;
            visibility: hidden;
            cursor: pointer;
            border-radius: 50%;
            transition: all 0.2s ease;

            &:hover {
                background-color: rgba($gray, 0.4);
                box-shadow: 0 0 0 5px rgba($gray, 0.4);
            }
        }

        .download-spinner {
            position: absolute;
            top: -2px;
            left: 2px;
            z-index: 2;

            &:hover {
                cursor: default;
            }

            ::ng-deep circle {
                stroke: $gray;
            }
        }

        &--loading {
            &:nth-child(2n) {
                .title {
                    .loading-box {
                        width: 68px;
                    }
                }

                .tenure {
                    .loading-box {
                        width: 80%;
                    }
                }

                .address {
                    .loading-box:not(.square) {
                        width: 60%;
                    }
                }
            }
        }
    }

    &__col {
        position: relative;
        padding: 15px 16px;
        border-bottom: 1px solid rgba($gray, 0.3);
        line-height: 16px;

        @include truncate(0px);

        &:first-of-type {
            min-width: 54px;
            padding-left: 24px;
        }

        &:last-of-type {
            padding-right: 35px;
        }

        &--head {
            position: sticky;
            top: 0;
            background-color: #fff;
            color: $dark-gray;
            font-size: 10px;
            text-align: start;
            cursor: pointer;
            z-index: 100;
        }

        &.reorder {
            width: 4%;
            padding: 5px 0 0 16px;
            cursor: grab;
            user-select: none;
        }

        &.reorder-col {
            width: 4%;
        }

        &.type {
            width: 7%;
            min-width: 60px;
        }

        &.document {
            width: 10%;
            min-width: 110px;
        }

        &.title {
            width: 11%;
            min-width: 100px;
        }

        &.tenure {
            width: 10%;
            min-width: 95px;
        }

        &.date {
            width: 14%;
            min-width: 132px;
        }

        &.address {
            width: 48%;

            .loading-box {
                width: 70%;
            }
        }

        .loading-box {
            width: 100%;
            height: 16px;
            border-radius: 5px;
            background-color: $loading-pulse-background-color;
            animation: loadingPulse 1s infinite ease-in-out;

            &.square {
                width: 16px;
            }

            &.close {
                position: absolute;
                top: 16px;
                right: 13px;
            }
        }
    }

    &__col-remove {
        position: absolute;
        top: 0;
        right: 10px;
        bottom: 0;
        display: flex;

        .remove-icon {
            margin: auto;
            width: 24px;
            height: 24px;
            opacity: 0.6;
            cursor: pointer;
            transition: opacity 0.2s ease;

            &:hover {
                opacity: 1;
            }
        }
    }

    &__load-link {
        position: relative;
        height: 14px;
    }
}

.preview {
    justify-content: space-between;
    gap: 30px;
    height: $row-height;
    padding: 0 20px;
    border-bottom: 1px solid rgba($gray, 0.3);
}

.placeholder {
    height: $row-height;
}

@keyframes pulse {
    0% {
        background-color: rgba($loading-placeholder-color, 1);
    }

    50% {
        background-color: rgba($loading-placeholder-color, 0.5);
    }

    100% {
        background-color: rgba($loading-placeholder-color, 1);
    }
}
