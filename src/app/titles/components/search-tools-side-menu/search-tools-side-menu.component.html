<div
    class="other-options"
>
    <header class="other-options__header">
        <avl-fixed-toggle
            *ngIf="isRosEnabled$ | async"
            class="tools-toggle"
            (toggled)="changeSearchRegistry($event)"
            [isChecked]="isToggleChecked()"
        >
            <div
                unchecked-label
                class="tools-toggle__label"
            >
                <mat-icon
                    class="tools-toggle__label-icon"
                    svgIcon="hmlr-b&w"
                ></mat-icon>
                HMLR
            </div>
            <div
                checked-label
                class="tools-toggle__label"
            >
                <mat-icon
                    class="tools-toggle__label-icon"
                    svgIcon="ros-b&w"
                ></mat-icon>
                ScotLIS
            </div>
        </avl-fixed-toggle>
        <h3 class="other-options__title">Other Options</h3>
        <p
            [ngSwitch]="searchRegistry"
            class="other-options__subtitle"
        >
            <span *ngSwitchCase="searchRegistryType.hmlr">
                Don’t have your titles? Either
                {{ (isMapSearchEnabled$ | async) ? 'select them on a map, ' : '' }}
                {{ (isSamEnabled$ | async) ? 'carry out a Search of the Avail Map (SAM) search, ' : '' }}
                carry out a PN1-style search, or search the Land Registry
            </span>
            <span *ngSwitchCase="searchRegistryType.ros">
                Don’t have your title sheets? Search the ScotLIS
            </span>
        </p>
    </header>

    <div
        [ngSwitch]="searchRegistry"
        class="tools-container"
    >
        <ng-container *ngSwitchCase="searchRegistryType.hmlr">
            <div
                *ngIf="isMapSearchEnabled$ | async"
                class="option-card"
            >
                <div class="option-card__img-container">
                    <img
                        class="option-card__img"
                        src="../../../../assets/icons/icon_map_search.svg"
                        alt="Map search"
                    >
                </div>
                <button
                    class="option-card__button avl-btn"
                    type="button"
                    [disabled]="isOnboardingActive$ | async"
                    (click)="openMap()"
                >
                    Find on map
                </button>
            </div>

            <div
                class="option-card"
            >
                <div class="option-card__img-container">
                    <img
                        class="option-card__img"
                        src="../../../../assets/icons/icon_coat_of_arms.svg"
                        alt="Coat of arms"
                    >
                </div>
                <button
                    class="option-card__button avl-btn"
                    type="button"
                    [disabled]="isOnboardingActive$ | async"
                    (click)="openCompanyNameSearch()"
                >
                    Do company PN1-style search
                </button>
            </div>

            <div
                class="option-card"
            >
                <div class="option-card__img-container">
                    <img
                        class="option-card__img"
                        src="../../../../assets/icons/icon_land_registry_colorless.svg"
                        alt="Colorless land registry"
                    >
                </div>
                <button
                    class="option-card__button avl-btn"
                    type="button"
                    [disabled]="isOnboardingActive$ | async"
                    (click)="openTitleNumberSearch()"
                >
                    Search land registry
                </button>
            </div>
        </ng-container>

        <ng-container *ngSwitchCase="searchRegistryType.ros">
            <div
                *ngIf="isRosEnabled$ | async"
                class="option-card"
            >
                <div class="option-card__new-label"></div>

                <div class="option-card__img-container">
                    <img
                        class="option-card__img"
                        src="../../../../assets/icons/icon_ros_logo.svg"
                        alt="ScotLIS icon"
                    >
                </div>
                <button
                    class="option-card__button avl-btn"
                    type="button"
                    [disabled]="isOnboardingActive$ | async"
                    (click)="openTitleNumberSearch()"
                >
                    Search ScotLIS
                </button>
            </div>
        </ng-container>
    </div>
</div>
