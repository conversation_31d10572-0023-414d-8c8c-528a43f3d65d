@import '~assets/sass/mixins';

.other-options {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
    padding: 11px 16px;
    height: 100%;
    background-color: white;
    border-radius: 5px;
    overflow: hidden;

    &__header {
        display: flex;
        flex-direction: column;
        gap: 10px;
        text-align: center;
        width: 100%;
    }

    &__title {
        font-weight: 700;
        font-size: 16px;
        line-height: 18px;
        color: #4c5263;
    }

    &__subtitle {
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        color: #96969e;
    }
}

.option-card {
    position: relative;
    width: 100%;
    height: 100%;
    max-height: calc(100% / 3 - 50px / 3);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0 15px 14px;
    background: #f3f3f4;
    border-radius: 5px;
    overflow: hidden;

    &__img-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        flex: 1;
    }

    &__img {
        padding: 10px;
        width: 80px;
    }

    &__button {
        width: 100%;
        height: 40px;
        padding: unset;
        background-color: #49536b;
    }

    &__new-label {
        --width: 73.45px;
        --box-shift: -36.725px;
        position: absolute;
        top: var(--box-shift);
        right: var(--box-shift);
        display: flex;
        align-items: flex-end;
        justify-content: center;
        width: var(--width);
        height: var(--width);
        padding-bottom: 2px;
        background: #a679ff;
        text-align: center;
        transform: rotate(45deg);
        z-index: 100;

        &::after {
            content: 'NEW';
            font-weight: 700;
            font-size: 12px;
            line-height: 14px;
            color: white;
        }
    }

    @media (max-height: 799px) {
        padding: 14px 15px;
        min-height: unset;

        &__img {
            height: auto;
            max-height: 50px;
            padding: 10px;
        }

        &.option-card {
            justify-content: center;
        }
    }

    @media (max-height: 489px) {
        padding: 14px 15px;
        min-height: unset;

        &__img-container {
            display: none;
            background: blue;
        }

        &.option-card {
            justify-content: center;
        }
    }
}

.tools-toggle {
    margin-bottom: 15px;
    width: 100%;

    &__label {
        display: flex;
        gap: 12px;
        justify-content: center;
        align-items: center;
    }

    &__label-icon {
        width: 16px;
        height: 16px;
    }
}

.tools-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
    height: 100%;
    width: 100%;
}
