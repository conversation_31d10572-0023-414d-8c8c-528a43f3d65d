import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { SearchRegistry } from '../../enums/search-register.enum';
import { RegistrySearchType } from '@enums';
import { Observable, Subscription } from 'rxjs';
import { ProfileService } from '@services';
import { LandRegistryDialogQuery, LandRegistryDialogService } from '../../store/land-registry-dialog';
import { OnboardingManageService } from '../../../onboarding/services';

@Component({
    selector: 'avl-search-tools-side-menu',
    templateUrl: './search-tools-side-menu.component.html',
    styleUrls: ['./search-tools-side-menu.component.scss'],
})
export class SearchToolsSideMenuComponent implements OnInit, OnDestroy {
    public readonly searchRegistryType = SearchRegistry;

    @Input()
    public searchRegistry: SearchRegistry = SearchRegistry.hmlr;

    @Output()
    public searchModalOpened = new EventEmitter<RegistrySearchType>();

    @Output()
    public searchRegistryChanged = new EventEmitter<SearchRegistry>();

    public isMapSearchEnabled$: Observable<boolean>;
    public isSamEnabled$: Observable<boolean>;
    public isRosEnabled$: Observable<boolean>;
    public isOnboardingActive$ = new Observable<boolean>();
    private readonly subscriptions = new Subscription();

    constructor(
        private readonly profileService: ProfileService,
        private readonly landRegistryDialogService: LandRegistryDialogService,
        private readonly landRegistryDialogQuery: LandRegistryDialogQuery,
        private readonly onboarding: OnboardingManageService,
    ) {
    }

    public ngOnInit(): void {
        this.isMapSearchEnabled$ = this.profileService.isMapSearchEnabled$;
        this.isSamEnabled$ = this.profileService.isSamEnabled$;
        this.isRosEnabled$ = this.profileService.isRosEnabled$;
        this.isOnboardingActive$ = this.onboarding.isOnboardingActive();

        this.switchRegistryIfRosDisabled();
    }

    public ngOnDestroy(): void {
        this.subscriptions.unsubscribe();
    }

    public openMap(): void {
        this.searchModalOpened.emit(RegistrySearchType.map);
    }

    public openCompanyNameSearch(): void {
        this.searchModalOpened.emit(RegistrySearchType.ownership);
    }

    public openTitleNumberSearch(): void {
        this.searchModalOpened.emit(RegistrySearchType.titleNumber);
    }

    public changeSearchRegistry(isRos: boolean): void {
        const registry = isRos ? SearchRegistry.ros : SearchRegistry.hmlr;

        this.searchRegistry = registry;
        this.searchRegistryChanged.emit(registry);
    }

    public isToggleChecked(): boolean {
        return this.searchRegistry === SearchRegistry.ros;
    }

    private switchRegistryIfRosDisabled(): void {
        const sub = this.profileService.isRosEnabled$
            .subscribe((isRosEnabled) => {
                const isRos = this.landRegistryDialogQuery.getValue().registry === SearchRegistry.ros;

                if (!isRosEnabled && isRos) {
                    this.landRegistryDialogService.changeSearchRegistry(SearchRegistry.hmlr);
                }
            });
        this.subscriptions.add(sub);
    }
}
