import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output } from '@angular/core';
import { tap } from 'rxjs/operators';
import { LandRegistryDialogQuery, LandRegistryDialogService } from 'app/titles/store/land-registry-dialog';
import { Observable, Subscription } from 'rxjs';
import { ITitleInfo } from '../../../types';
import { MapSearchQuery, MapSearchService } from '../../../store';

@Component({
    selector: 'avl-map-sidebar-details',
    templateUrl: './map-sidebar-details.component.html',
    styleUrls: ['./map-sidebar-details.component.scss'],
})
export class MapSidebarDetailsComponent implements OnInit, OnChanges, OnDestroy {
    @Input()
    public title: ITitleInfo;

    @Output()
    public backButtonClicked = new EventEmitter();

    public isInBasket = false;
    public isBackButtonVisible$: Observable<boolean>;

    private readonly sub = new Subscription();

    constructor(
        private readonly mapSearchService: MapSearchService,
        private readonly ref: ChangeDetectorRef,
        private readonly mapSearchQuery: MapSearchQuery,
        private readonly landRegistryDialogQuery: LandRegistryDialogQuery,
        private readonly landRegistryDialogService: LandRegistryDialogService,
    ) {
    }

    public ngOnInit(): void {
        this.sub.add(
            this.landRegistryDialogQuery.isTitleAddedToBasket$(this.title.titleNumber)
                .pipe(
                    tap((isInBasket) => {
                        this.isInBasket = isInBasket;
                        setTimeout(() => this.ref.detectChanges());
                    }),
                )
                .subscribe(),
        );
        this.isBackButtonVisible$ = this.mapSearchQuery.isSelectedMultipleFeatures();
    }

    public ngOnChanges(): void {
        this.ref.detectChanges();
    }

    public ngOnDestroy(): void {
        this.sub.unsubscribe();
    }

    public basketToggle(): void {
        if (this.isInBasket) {
            this.landRegistryDialogService.removeTitleFromBasket(this.title.titleNumber);
        } else {
            this.landRegistryDialogService.addTitleToBasket(this.title);
        }
    }

    public emitToBack(): void {
        const titleNumber = this.title.titleNumber;

        this.mapSearchService.setPreviousShownTitleId(titleNumber);
        this.mapSearchService.restoreStateBeforeFocusing();
        this.mapSearchService.resetTitleData();
        this.mapSearchService.resetTemporaryHighlighting();
        this.backButtonClicked.emit();
    }

    public viewOnGoogleMaps(): void {
        this.mapSearchService.pointOnSelectedTitleOnGoogleMaps();
    }
}
