<div class="map-container">
    <avl-map
        [zoom]="zoom"
        [center]="center"
        [isClickable]="isMapInteractionAvailable$ | async"
        [isSelectionUnderPinBlocked]="isSelectionUnderPinBlocked$ | async"
        (featuresSelected)="onFeaturesSelected($event)"
        (mapClicked)="onMapClick($event)"
        (mapInitialized)="onMapInit($event)"
        (centerChanged)="onMapCenterChange($event)"
        (zoomChanged)="onMapZoomChange($event)"
    ></avl-map>
    <avl-map-sidebar *ngIf="isSidebarVisible$ | async"></avl-map-sidebar>
    <avl-map-controls
        [isLoading]="isMapLoading$ | async"
        [isSamUnavailable]="isSamUnavailable$ | async"
        (controlsChanged)="onControlsChanged($event)"
    ></avl-map-controls>
    <avl-map-snackbar [messages]="samMessages$"></avl-map-snackbar>
</div>
