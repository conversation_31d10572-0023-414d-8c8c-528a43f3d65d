.card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 4px 8px 15px;

    &:hover {
        background: rgba(12, 106, 217, 0.05);
    }

    &_selected {
        background: rgba(12, 106, 217, 0.05);
    }
}

.card-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-value {
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: #000113;

    &--uppercase {
        text-transform: uppercase;
    }
}

.buttons {
    display: flex;
    gap: 6px;
}

.button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 93px;
    height: 33px;
    padding: 0;
    text-transform: uppercase;

    &:disabled {
        opacity: initial;
    }
}

.icon {
    display: block;
    justify-content: center;
    height: 100%;
    width: 100%;
    background-position-x: 50%;
    background-position-y: 50%;
    background-repeat: no-repeat;

    &-icon-checked {
        background-image: url("data:image/svg+xml,%3Csvg width='32' height='32' viewBox='0 0 32 32' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.1213 16.3642C9.7308 15.9737 9.09763 15.9737 8.70711 16.3642C8.31658 16.7547 8.31658 17.3879 8.70711 17.7784L12.9497 22.021C13.3403 22.4116 13.9734 22.4116 14.364 22.021C14.7545 21.6305 14.7545 20.9973 14.364 20.6068L10.1213 16.3642Z' fill='white'/%3E%3Cpath d='M24.2634 12.1213C24.6539 11.7308 24.6539 11.0976 24.2634 10.7071C23.8729 10.3166 23.2397 10.3166 22.8492 10.7071L12.9497 20.6066C12.5591 20.9971 12.5591 21.6303 12.9497 22.0208C13.3402 22.4113 13.9734 22.4113 14.3639 22.0208L24.2634 12.1213Z' fill='white'/%3E%3C/svg%3E");
    }
}
