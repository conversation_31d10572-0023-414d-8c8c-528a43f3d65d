@use 'assets/sass/variables' as *;

.list-container {
    padding: 8px 15px 0;
    border-bottom: 1px solid rgba(214, 215, 221, 0.5);
}

.list-item-container {
    border-bottom: 1px solid rgba(214, 215, 221, 0.5);
}

.list-title {
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    color: #96969e;
    text-align: center;
    text-transform: uppercase;
}

.list-description {
    margin-top: 9px;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    align-items: center;
    text-align: center;
    color: #96969e;
}

.header-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.download-button {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    border: 1px solid rgba(116, 142, 206, 1);
    border-radius: 4px;
    width: 33px;
    height: 33px;

    &:hover {
        opacity: 0.7;
    }
}

.add-all-button {
    display: flex;
    gap: 5px;
    justify-content: center;
    align-items: center;
    width: 93px;
    height: 33px;
    padding: 0;
    font-size: 12px;
    text-transform: uppercase;

    .icon-placeholder {
        position: relative;
        width: 16px;
        height: 16px;
    }

    .cart-icon {
        width: 16px;
        height: 16px;
    }

    &:disabled {
        opacity: initial;
    }

    &--done {
        background-color: $green;
    }
}

.scroll-container {
    height: calc(100% - 102px);
    overflow-y: scroll;
}

@keyframes highlight {
    0% {
        background: #ddf5fe;
    }

    100% {
        background: none;
    }
}

.highlight {
    animation: highlight 4s;
}
