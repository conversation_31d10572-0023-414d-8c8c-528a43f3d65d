<div class="list-container">
    <h1 class="list-title">
        Multiple titles
    </h1>
    <div class="list-description">
        Click on any title below to highlight it on the map
    </div>

    <div class="header-buttons">
        <div>
            <button
                class="clear-btn-styles download-button"
                matTooltip="Click to download the list of titles shown into an Excel spreadsheet."
                matTooltipClass="mat-tooltip tooltip__max-w330 tooltip__p8 tooltip__rounded4
               tooltip__text14px tooltip__gray-blue-bg"
                matTooltipPosition="right"
                (click)="downloadList()"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    height="22px"
                    viewBox="0 -960 960 960"
                    width="22px"
                    fill="rgba(116, 142, 206, 1)"
                >
                    <path
                        d="M480-320 280-520l56-58 104 104v-326h80v326l104-104 56 58-200
                        200ZM240-160q-33 0-56.5-23.5T160-240v-120h80v120h480v-120h80v120q0 33-23.5 56.5T720-160H240Z"
                    />
                </svg>
            </button>
        </div>

        <button
            class="avl-btn avl-btn--blue add-all-button"
            (click)="onAddAll()"
            [class.add-all-button--done]="isAllSelected$ | async"
            [disabled]="isBaskedPending$ | async"
        >
            <div class="icon-placeholder">
                <mat-icon
                    *ngIf="isAllSelected$ | async; else addToCartIconOrLoading"
                    class="cart-icon"
                    svgIcon="added-to-cart"
                    @iconTransition
                ></mat-icon>

                <ng-template #addToCartIconOrLoading>
                    <avl-spinner
                        *ngIf="isAllPending; else addToCartIcon"
                        primaryColor="white"
                        [diameter]="16"
                        @iconTransition
                    ></avl-spinner>
                    <ng-template #addToCartIcon>
                        <mat-icon
                            class="cart-icon"
                            svgIcon="add-to-cart"
                            @iconTransition
                        ></mat-icon>
                    </ng-template>
                </ng-template>
            </div>
            <span class="checkbox-text">Add All</span>
        </button>
    </div>
</div>

<div
    #scrollContainer
    class="scroll-container scroll-container--thin-scroll"
>
    <ng-container *ngFor="let item of list; trackBy: itemTrackBy">
        <div
            class="list-item-container"
            [id]="'title-number-' + item.titleNumber"
        >
            <avl-map-details-list-item
                [titleNumber]="item.titleNumber"
                [titleTenure]="item.tenure"
                [isDisabled]="isTitlePending(item.titleNumber)"
                [isInBasket]="isTitleInBasket(item.titleNumber)"
                [isHighlighted]="isTitleHighlighted(item.titleNumber)"
                (click)="onItemClicked(item)"
                (mouseenter)="onItemHovered(item)"
                (mouseleave)="onItemUnhovered(item)"
                (infoButtonClick)="onShowDetails(item)"
            ></avl-map-details-list-item>
        </div>
    </ng-container>
</div>
