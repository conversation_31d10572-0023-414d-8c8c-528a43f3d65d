import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    Renderer2,
    ViewChild,
} from '@angular/core';
import { Observable, Subject, tap } from 'rxjs';
import { ShortTitleInfo, TitleBasketItem } from '../../../types';
import { MapSearchQuery, MapSearchStore } from '../../../store';
import { LandRegistryDialogQuery, LandRegistryDialogService } from '../../../store/land-registry-dialog';
import { finalize, map, takeUntil } from 'rxjs/operators';
import { animate, style, transition, trigger } from '@angular/animations';
import { HmLandRegistryService } from '../../../services';
import { ExportTitlesService } from '../../../modules/map-search/services/export-titles.service';

@Component({
    selector: 'avl-map-details-list',
    templateUrl: './map-details-list.component.html',
    styleUrls: ['./map-details-list.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: [
        trigger('iconTransition', [
            transition(':enter', [
                style({ opacity: 0, transform: 'translateY(30px)' }),
                animate('300ms linear', style({ opacity: 1, transform: 'translateY(0)' })),
            ]),
            transition(':leave', [
                style({ opacity: 1, transform: 'translateY(0)' }),
                animate('100ms linear', style({ opacity: 0, transform: 'translateY(-30px)' })),
            ]),
        ]),
    ],
})
export class MapDetailsListComponent implements AfterViewInit, OnInit, OnDestroy {
    @Input()
    public list: ShortTitleInfo[];

    @Output()
    public showDetails = new EventEmitter<string>();

    @Output()
    public itemClicked = new EventEmitter<string>();

    @Output()
    public itemHovered = new EventEmitter<string>();

    @Output()
    public itemUnhovered = new EventEmitter<string>();

    public isAllSelected$: Observable<boolean>;
    public isBaskedPending$: Observable<boolean>;
    public isAllPending = false;
    public isAllSelected = false;

    @ViewChild('scrollContainer', { static: false })
    private readonly scrollContainer: ElementRef;

    private readonly destroy$ = new Subject<void>();

    constructor(
        private readonly mapSearchStore: MapSearchStore,
        private readonly mapSearchQuery: MapSearchQuery,
        private readonly landRegistryDialogService: LandRegistryDialogService,
        private readonly landRegistryDialogQuery: LandRegistryDialogQuery,
        private readonly renderer: Renderer2,
        private readonly landRegistry: HmLandRegistryService,
        private readonly exportTitlesService: ExportTitlesService,
    ) {
    }

    public ngOnInit(): void {
        this.isBaskedPending$ = this.landRegistryDialogQuery.selectIsAnyTitlePending();
        this.isAllSelected$ = this.landRegistryDialogQuery.selectBasketTitles()
            .pipe(
                map((basketTitles) => {
                    const basketTitleNumbers = basketTitles.map((item) => item.titleNumber);

                    return this.list.every((item) => basketTitleNumbers.includes(item.titleNumber));
                }),
                tap((isAllInBasket) => this.isAllSelected = isAllInBasket),
            );
        this.mapSearchQuery.sidePanelContentIsUpdated()
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
                const highlightedTitleNumber = this.mapSearchStore.getValue().permanentlyHighlightedTitleNumber;
                setTimeout(() => this.scrollIntoContainerIfElementHidden(highlightedTitleNumber), 300);
            });
    }

    public ngAfterViewInit(): void {
        // TODO check on this.activatedRoute.queryParams didnt return actular query params
        const previousShowTitleId = this.mapSearchStore.getValue().previousShowTitleId;
        if (previousShowTitleId) {
            this.scrollToAnchorAndHighlight(`${previousShowTitleId}`);
            this.mapSearchStore.update({ previousShowTitleId: null });
        }
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    public scrollToAnchorAndHighlight(anchorId: string): void {
        const container = this.scrollContainer?.nativeElement;
        const anchorElement = container?.querySelector(`#title-number-${anchorId}`);

        if (anchorElement) {
            anchorElement.scrollIntoView();
            this.renderer.addClass(anchorElement, 'highlight');

            setTimeout(() => {
                this.renderer.removeClass(anchorElement, 'highlight');
            }, 4000);
        }
    }

    public scrollIntoContainerIfElementHidden(anchorId: string): void {
        const container = this.scrollContainer?.nativeElement;
        const anchorElement = container?.querySelector(`#title-number-${anchorId}`);

        if (!this.isElementVisible(anchorElement)) {
            anchorElement?.scrollIntoView({ behavior: 'smooth' });
        }
    }

    public onShowDetails(item: ShortTitleInfo): void {
        this.showDetails.emit(item.titleNumber);
    }

    public onItemClicked(item: ShortTitleInfo): void {
        this.itemClicked.emit(item.titleNumber);
    }

    public onItemHovered(item: ShortTitleInfo): void {
        this.itemHovered.emit(item.titleNumber);
    }

    public onItemUnhovered(item: ShortTitleInfo): void {
        this.itemUnhovered.emit(item.titleNumber);
    }

    public itemTrackBy(index: number, item: ShortTitleInfo): string {
        return item.titleNumber;
    }

    public downloadList(): void {
        const titleNumbers = this.list;

        if (titleNumbers.length > 0) {
            this.exportTitlesService.exportTitlesExel(titleNumbers);
        }
    }

    public isTitlePending(titleNumber: string): boolean {
        return this.landRegistryDialogQuery.getIsTitlePending(titleNumber);
    }

    public isTitleInBasket(titleNumber: string): boolean {
        return this.landRegistryDialogQuery.getIsTitleInBasket(titleNumber);
    }

    public isTitleHighlighted(titleNumber: string): boolean {
        return this.mapSearchQuery.isTitleNumberPermanentlyHighlighted(titleNumber);
    }

    public onAddAll(): void {
        if (this.isAllSelected) {
            this.unselectAll();
        } else {
            this.selectAll();
        }
    }

    private unselectAll(): void {
        this.list.forEach((item) => this.landRegistryDialogService.removeTitleFromBasket(item.titleNumber));
    }

    private selectAll(): void {
        const titleNumbers = this.list.map((item) => item.titleNumber);

        this.isAllPending = true;
        this.landRegistryDialogService.addTitlesToBasketPendingList(titleNumbers);
        this.landRegistry.getPricing()
            .pipe(
                tap((pricing) => {
                    titleNumbers.forEach((title) => {
                        const basketItem: TitleBasketItem = {
                            titleNumber: title,
                            cost: pricing,
                        };
                        this.landRegistryDialogService.addTitleToBasket(basketItem);
                    });
                }),
                finalize(() => {
                    this.isAllPending = false;
                    this.landRegistryDialogService.removeTitlesFromBasketPendingList(titleNumbers);
                }),
            )
            .subscribe();
    }

    private isElementVisible(element: HTMLElement): boolean {
        return !!(element?.offsetWidth || element?.offsetHeight);
    }
}
