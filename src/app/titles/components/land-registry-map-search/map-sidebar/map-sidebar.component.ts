import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { animate, animation, keyframes, style, transition, trigger } from '@angular/animations';

import { Observable, Subscription } from 'rxjs';
import { MapSearchQuery, MapSearchService } from '../../../store';
import { ITitleInfo, ShortTitleInfo } from '../../../types';
import { map } from 'rxjs/operators';

@Component({
    selector: 'avl-map-sidebar',
    templateUrl: './map-sidebar.component.html',
    styleUrls: ['./map-sidebar.component.scss'],
    animations: [
        trigger('fadeInLeft', [
            transition(':enter', [
                style({ visibility: 'hidden' }),
                animation([
                    animate(
                        '{{duration}}ms {{delay}}ms',
                        keyframes([
                            style({
                                visibility: 'visible',
                                opacity: 0,
                                transform: 'translate3d(-{{translate}}, 0, 0)',
                                easing: 'ease',
                                offset: 0,
                            }),
                            style({ opacity: 1, transform: 'translate3d(0, 0, 0)', easing: 'ease', offset: 1 }),
                        ]),
                    ),
                ]),
            ], {
                params: {
                    delay: 0,
                    duration: 500,
                    translate: '100%',
                },
            }),
        ]),
    ],
})
export class MapSidebarComponent implements OnInit, OnDestroy {
    public detailsData?: ITitleInfo;
    public isLoading$: Observable<boolean>;
    public isNotLoading$: Observable<boolean>;
    public list$: Observable<ShortTitleInfo[]>;
    public isDownloadButtonVisible$: Observable<boolean>;

    private readonly sub = new Subscription();

    constructor(
        private readonly mapSearchQuery: MapSearchQuery,
        private readonly ref: ChangeDetectorRef,
        private readonly mapSearchService: MapSearchService,
    ) {
    }

    public ngOnInit(): void {
        this.sub.add(
            this.mapSearchQuery.select('details')
                .subscribe((details) => {
                    this.detailsData = details;
                    this.ref.detectChanges();
                }),
        );
        this.isLoading$ = this.mapSearchQuery.select('isSidePanelLoading');
        this.isNotLoading$ = this.mapSearchQuery.select('isSidePanelLoading')
            .pipe(map((isLoading) => !isLoading));
        this.list$ = this.mapSearchQuery.getUniqueSelectedTitles();
    }

    public ngOnDestroy(): void {
        this.sub.unsubscribe();
    }

    public closeSidebar(): void {
        this.mapSearchService.resetTitleData();
        this.mapSearchService.closeSidePanelAndResetHighlighting();
        this.mapSearchService.setMarkerPosition(null);
    }

    public showRegistryDetails(titleNumber: string): void {
        this.mapSearchService.fetchTitleData(titleNumber);
        this.mapSearchService.focusFeature(titleNumber);
    }

    public onEmitToBack(): void {
        this.detailsData = null;
    }

    public onTitleClicked(titleNumber: string): void {
        this.mapSearchService.highlightFeaturePermanently(titleNumber);
    }

    public onTitleHovered(titleNumber: string): void {
        this.mapSearchService.highlightTitleTemporary(titleNumber);
    }

    public onTitleUnhovered(): void {
        this.removeHover();
    }

    public removeHover(): void {
        this.mapSearchService.highlightTitleTemporary('');
    }

}
