.content-sidebar {
    position: absolute;
    z-index: 999;
    top: 0;
    background: white;
    left: -1px;
    height: 100%;
    width: 360px;
    transition: 0.5s out;
    box-shadow: 0 4px 8px rgba(38, 50, 56, 0.24);

    &__close-btn {
        cursor: pointer;
        position: absolute;
        top: 6px;
        right: 11px;
    }

    &__close-btn:hover {
        opacity: 0.7;
        transition: opacity 150ms;
    }
}

.loading-container {
    display: flex;
    align-content: center;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    height: 80%;
    flex-direction: column;
}

.details-loading-image {
    width: 143px;
    height: 128.41px;
    background-color: #ffffff00;
    -webkit-backface-visibility: hidden;
}
