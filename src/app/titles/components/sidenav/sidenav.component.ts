import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { NotifyCountersState } from 'app/titles/store';
import { ActivatedRoute } from '@angular/router';
import { AvailToolKey } from '@enums';
import { OnboardingManageService } from '../../../onboarding/services';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';
import { ProfileService } from '@services';

@Component({
    selector: 'avl-sidenav',
    templateUrl: './sidenav.component.html',
    styleUrls: ['./sidenav.component.scss'],
})
export class SidenavComponent implements OnDestroy {
    public readonly tools = AvailToolKey;

    @Input()
    public counters: NotifyCountersState;

    @Output()
    public newProjectCreated = new EventEmitter<void>();

    @Output()
    public closed = new EventEmitter<void>();

    public isTeamworkEnabled$ = this.profile.isTeamworkEnabled$.asObservable();

    constructor(
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly route: ActivatedRoute,
        private readonly onboarding: OnboardingManageService,
        private readonly profile: ProfileService,
    ) {
    }

    public ngOnDestroy(): void {
        this.onboarding.skipBranch();
    }

    public createNewProject(): void {
        this.newProjectCreated.emit();
        this.close();
    }

    public close(): void {
        this.closed.emit();
    }

    public getFolderId(): string {
        const routeSnapshot = this.route.snapshot;
        const folderId = routeSnapshot.queryParamMap.get('fid');

        return this.projectDetailsQuery.projectId || folderId;
    }
}
