@use 'assets/sass/variables' as *;

.sidenav {
    &__head-bg {
        width: 304px;
    }

    &__content {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding-top: 0;
    }

    &__list {
        height: 100%;
        padding: 4px 8px 0;
        font-weight: 500;
    }

    &__list-btn {
        border: none;
        background-color: transparent;
        cursor: pointer;
    }

    &__list-item {
        margin: 8px 0;
        border-radius: 4px;
        background-color: #fff;
        transition: all 0.2s linear;

        &:hover {
            box-shadow: 0 4px 8px rgba(48, 79, 254, 0.04);
            background-color: rgba(#6f90d4, 0.16);
        }

        &.mat-list-item {
            font-size: 14px;
            line-height: 24px;
            height: 48px;

            .mat-list-item-content {
                padding: 0 12px;
            }
        }

        .mat-icon {
            margin-right: 32px;
        }
    }

    &__list-icon {
        color: #6f90d4;
    }

    &__notification-bubble {
        position: absolute;
        top: 12px;
        right: 12px;
        min-width: 24px;
        padding: 0 3px;
        height: 24px;
        background-color: $dark-green;
        color: #fff;
        line-height: 24px;
        text-align: center;
        border-radius: 3px;
    }

    &__apps {
        height: 150px;
    }
}
