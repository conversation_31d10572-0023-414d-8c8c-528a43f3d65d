import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Sort } from '@angular/material/sort';
import { DocumentsQuery, SearchFilesQuery, SearchFilesService } from '../../store';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ID, Order } from '@datorama/akita';
import { ConfirmDialogComponent, IConfirmDialogData } from '@shared/components/dialogs/confirm-dialog/confirm-dialog.component';
import { tableAnimation } from '../../../core/animations/table-rows.animation';
import { SelectItem } from '@shared/components/multiple-select/multiple-select.component';
import { SearchFile } from '../../types/search-file.type';
import { first, map, tap } from 'rxjs/operators';
import { REMOVE_SEARCH_FILE_CONFIRM_DATA } from '@constants';
import { SearchFilesApi } from '../../api/search-files.api';
import { Observable } from 'rxjs';
import { DocumentApi } from '../../api';
import { SearchType, searchTypeNames } from '../../enums';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';

@Component({
    selector: 'avl-search-files-table',
    templateUrl: './search-files-table.component.html',
    styleUrls: ['./search-files-table.component.scss'],
    animations: [tableAnimation],
})
export class SearchFilesTableComponent implements OnInit {
    public readonly searchTypeNames = searchTypeNames;

    @Input()
    public isLoading: boolean;

    @Input()
    public defaultSearchType: string = null;

    @Input()
    public emptyRowsCount: number;

    @Output()
    public uploadingCanceled = new EventEmitter<string>();

    public emptyRows: number[];
    public allowedSearchFileTypeItems: SelectItem[] = [];
    public uploadedTitleOptions$: Observable<SelectItem[]>;
    public isLocalSearchType = false;

    private _files: SearchFile[];
    private lastSortParams?: Sort;

    constructor(
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly documentsQuery: DocumentsQuery,
        private readonly dialog: MatDialog,
        private readonly searchFilesApi: SearchFilesApi,
        private readonly searchFilesService: SearchFilesService,
        private readonly searchFilesQuery: SearchFilesQuery,
        private readonly documentApi: DocumentApi,
    ) {
    }

    public get files(): SearchFile[] {
        return this._files;
    }

    @Input()
    public set files(value: SearchFile[]) {
        if (this.lastSortParams) {
            this.sortFiles(this.lastSortParams);
        } else {
            this._files = value;
        }
    }

    public ngOnInit(): void {
        this.uploadedTitleOptions$ = this.documentsQuery.documents$
            .pipe(
                map((documents) =>
                    documents.map((document) => ({ id: document.titleNumber, value: document.titleNumber })),
                ),
            );
        this.emptyRows = Array(this.emptyRowsCount);

        if (this.defaultSearchType) {
            this.isLocalSearchType = this.defaultSearchType === SearchType.local;
        } else {
            this.searchFilesApi.getAllowedDocTypes({ isCacheEnabled: true })
                .subscribe((types) => {
                    const typeIds = Object.keys(types);
                    this.allowedSearchFileTypeItems = [];
                    typeIds.forEach((typeId) => this.allowedSearchFileTypeItems.push({ id: typeId, value: types[typeId] }));
                });
        }
    }

    public getDownloadHref(documentId: string): string {
        const folderId = this.projectDetailsQuery.projectId;

        return this.documentApi.getTitlesDocumentUrl(folderId, documentId);
    }

    public sortFiles(sort: Sort): void {
        this.lastSortParams = sort;
        const sortBy = sort.active as keyof SearchFile;
        const sortByOrder = sort.direction as Order;

        this.searchFilesService.setSortParams(sortBy, sortByOrder);
        this.searchFilesQuery.selectSortedFiles$()
            .pipe(first())
            .subscribe((sortedList) => this._files = sortedList);
    }

    public onLinkedTitlesChanged(fileId: string, selectedTitleNumbers: string[]): void {
        this.searchFilesService.linkTitles(fileId, selectedTitleNumbers);
    }

    public onFileTypeChanged(fileId: string, fileType: string): void {
        this.searchFilesService.setType(fileId, fileType);
    }

    public onRemoveOrCancel(id: string, { isCancel }: { isCancel: boolean } = { isCancel: false }): void {
        this.showRemoveConfirmDialog(REMOVE_SEARCH_FILE_CONFIRM_DATA)
            .afterClosed()
            .pipe(
                tap((isRemoved) => {
                    if (isRemoved) {
                        this.searchFilesService.removeOrCancelFile(id);

                        if (isCancel) {
                            this.uploadingCanceled.emit(id);
                        }
                    }
                }),
            )
            .subscribe();
    }

    public trackByDocuments(index: number, file: SearchFile): ID {
        return file.id;
    }

    public adjustSelectDropDownPosition(selectId: string): void {
        const selectDropdownId = `${selectId}-panel`;
        const component = document.getElementById(selectDropdownId);
        const dropdownRect = component.getBoundingClientRect();
        const isDropdownOutsideWindow = dropdownRect.bottom > window.innerHeight;

        if (isDropdownOutsideWindow) {
            component.classList.add('input-select__panel_above');
        } else {
            component.classList.remove('input-select__panel_above');
        }
    }

    private showRemoveConfirmDialog(dialogContent: IConfirmDialogData): MatDialogRef<ConfirmDialogComponent> {
        return this.dialog.open(ConfirmDialogComponent, { panelClass: 'confirm-dialog', data: dialogContent });
    }
}
