@import '~assets/sass/variables';

:host {
    display: flex;
    flex: 1;
    flex-direction: column;
    max-height: calc(100vh - 303px);
    overflow: auto;
    border-radius: 5px;
}

.collection {
    &__table {
        &-container {
            &.schedules-table {
                table {
                    th.mat-header-cell {
                        position: sticky;
                        padding-left: 24px !important;
                        top: 0;
                        height: 45px;
                        z-index: 6;
                        border-bottom: 1px solid rgba(204, 204, 204, 0.3);
                        background: #fff;
                    }

                    td.mat-cell {
                        position: relative;
                        box-sizing: border-box;
                        padding: 12px 8px 12px 24px;
                        vertical-align: center;
                        font-family: Arial;
                        font-size: 12px;
                        line-height: 16px;
                        letter-spacing: 0em;
                        text-align: left;
                        height: 75px;
                        min-height: 75px;

                        &.expanded {
                            z-index: 1;
                            padding: 0 24px 12px 24px;
                            border-radius: 0;
                            top: -2px;
                            height: 38px;
                            min-height: 38px;
                            background: #fff;
                        }

                        button {
                            padding: 4px 10px;
                            height: 40px;
                            width: 100%;
                        }
                    }
                }

                .mat-column {
                    &-icon {
                        padding-right: 0 !important;
                    }

                    &-preview {
                        width: 127px;
                        min-width: 127px;
                        padding: 0 15px 0 0 !important;
                    }

                    &-action {
                        width: 224px;
                        min-width: 224px;
                        padding: 0 24px 0 4px !important;
                    }

                    &-type {
                        width: 17%;
                    }

                    &-provider {
                        width: 10%;
                    }

                    &-cost {
                        width: 10%;
                    }

                    &-description {
                        width: 50%;
                        padding: 12px 24px !important;
                    }

                    &-expandedDetail {
                        position: relative;

                        &:after {
                            content: '';
                            position: absolute;
                            left: 0px;
                            width: 100%;
                            height: 15px;
                            border-bottom: 1px solid rgba(204, 204, 204, 0.3);
                            background: #fff;
                        }
                    }
                }

                .mat-row {
                    position: relative;

                    &.new {
                        td {
                            &:first-child {
                                &:after {
                                    content: '';
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    z-index: 3;
                                    width: 0;
                                    height: 0;
                                    border: 16px solid;
                                    border-color: $dark-green transparent transparent $dark-green;
                                }

                                &:before {
                                    content: 'New';
                                    position: absolute;
                                    text-transform: uppercase;
                                    font-size: 8px;
                                    top: 4px;
                                    left: 3px;
                                    transform: rotate(-45deg);
                                    z-index: 5;
                                    font-family: 'Arial';
                                    font-weight: 700;
                                    color: #fff;
                                }
                            }
                        }
                    }

                    &.beta {
                        td {
                            &:first-child {
                                &:after {
                                    content: '';
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    z-index: 3;
                                    width: 0;
                                    height: 0;
                                    border: 16px solid;
                                    border-color: $mid-blue transparent transparent $mid-blue;
                                }

                                &:before {
                                    content: 'Beta';
                                    position: absolute;
                                    text-transform: uppercase;
                                    font-size: 8px;
                                    top: 4px;
                                    left: 3px;
                                    transform: rotate(-45deg);
                                    z-index: 5;
                                    font-family: 'Arial';
                                    font-weight: 700;
                                    color: #fff;
                                }
                            }
                        }
                    }
                }
            }

            .error {
                padding: 16px;
                display: none;
                height: 0;
                border: none;
            }

            .visible {
                display: table-row;
                height: 100%;
                transition-duration: 12s;
                transition-property: height;
            }
        }

        tr.mat-header-row {
            height: 45px;
        }
    }
}

.width-207px {
    width: 207px !important;
}
