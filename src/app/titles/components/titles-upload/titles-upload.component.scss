@import '~assets/sass/mixins';

:host {
    display: flex;
    flex-direction: column;
    height: calc(100% - 64px);
}

.page-container {
    display: flex;
    gap: 28px;
    height: 100%;
}

.upload-tools {
    @include minW(768px) {
        flex-basis: 50%;
        width: 50%;
    }

    @include minW(992px) {
        flex-basis: 70%;
        width: 70%;
    }

    @include minW(1900px) {
        flex-basis: 100%;
        width: 100%;
    }

    display: flex;
    flex-direction: column;
    width: 60%;
}

.titles-btn {
    min-height: 60px;
    margin-top: 16px;
    margin-bottom: 20px;
    padding-top: 22px;
    padding-bottom: 22px;
}

.titles-documents {
    flex: 1 100%;
    min-height: 94px;
}

.other-options {
    height: calc(100% - 16px);

    @include minW(768px) {
        flex-basis: 50%;
    }

    @include minW(992px) {
        flex-basis: 30%;
    }

    @include minW(1900px) {
        max-width: 500px;
    }
}
