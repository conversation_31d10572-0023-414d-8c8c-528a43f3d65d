import { NgModule } from '@angular/core';

import { SharedModule } from '@shared/shared.module';
import { TitlesResolve } from './resolvers/titles.resolve';
import { OnboardingModule } from 'app/onboarding/onboarding.module';

import { BookmarksApi, Document<PERSON>pi, FiledCopy<PERSON>pi, FolderApi, HmlrApi, PurchaseApi, ReportApi, SchedulesApi } from './api';
import {
    DocumentsService,
    FolderService,
    GeneratedReportsService,
    HmLandRegistryService,
    HttpStatusesHandlerService,
    ImanageService,
    LandRegistryService,
    NotifyCountersService,
    PurchaseService,
    RefreshService,
    ReportGenerationHandlerService,
    ReportService,
    ScotlandLandRegistryService,
    ThemesService,
} from './services';

import {
    DocumentsQuery,
    DocumentsStore,
    FailedDocumentsQuery,
    FailedDocumentsService,
    FailedDocumentsStore,
    FailedPurchasesQuery,
    FailedPurchasesStore,
    FiledCopiesDialogQuery,
    FiledCopiesDialogService,
    FiledCopiesDialogStore,
    FiledCopiesUploadQuery,
    FiledCopiesUploadService,
    FiledCopiesUploadStore,
    GeneratedReportsQuery,
    GeneratedReportsStore,
    MapSearchQuery,
    MapSearchService,
    MapSearchStore,
    NotifyCountersQuery,
    NotifyCountersStore,
    PurchasedFilesQuery,
    PurchasedFilesStore,
    PurchasedSelectedFilesQuery,
    PurchasedSelectedFilesStore,
    ReportOptionsQuery,
    ReportOptionsStore,
    ScheduleFilesUploadQuery,
    ScheduleFilesUploadService,
    ScheduleFilesUploadStore,
    SchedulesQuery,
    SchedulesStore,
    SearchFilesQuery,
    SearchFilesService,
    SearchFilesStore,
    SearchResultsQuery,
    SearchResultsStore,
} from './store';

import { TitleRegistersComponent } from './components/title-registers/title-registers.component';
import { PurchasedPageComponent } from './components/purchased-page/purchased-page.component';
import { ReportsPageComponent } from './components/reports-page/reports-page.component';
import { TitlesUploadComponent } from './components/titles-upload/titles-upload.component';
import { DocumentsComponent } from './components/documents/documents.component';
import { DropAreaComponent } from './components/drop-area/drop-area.component';

import { LandRegistryDialogComponent } from './components/dialogs/land-registry-dialog/land-registry-dialog.component';
import { ReportGenerationDialogComponent } from './components/dialogs/report-generation-dialog/report-generation-dialog.component';
import { ReportsTableComponent } from './components/reports-table/reports-table.component';
import { PurchasedFilesTableComponent } from './components/purchased-files-table/purchased-files-table.component';
import { PurchaseDownloadButtonComponent } from './components/buttons/purchase-download-button/purchase-download-button.component';
import { SelectionToolbarComponent } from './components/selection-toolbar/selection-toolbar.component';
import { DownloadReportDialogComponent } from './components/dialogs/download-report-dialog/download-report-dialog.component';
import { SidenavComponent } from './components/sidenav/sidenav.component';
import { LandRegistrySearchComponent } from './components/land-registry-search/land-registry-search.component';
import { LandRegistryResultsTableComponent } from './components/land-registry-results-table/land-registry-results-table.component';
import { PurchaseTitlesButtonComponent } from './components/buttons/purchase-titles-button/purchase-titles-button.component';
import { DownloadReportButtonComponent } from './components/buttons/download-report-button/download-report-button.component';
import { SchedulePurchasesButtonComponent } from './components/buttons/schedule-purchases-button/schedule-purchases-button.component';
import { TitlesSchedulesTableComponent } from './components/titles-schedules/titles-schedules.component';
import { SchedulesTableComponent } from './components/schedules-table/schedules-table.component';
import { SchedulesResolve } from './resolvers/schedules.resolve';
import { DownloadSchedulePreviewDialogComponent } from './components/dialogs/download-schedule-preview-dialog/download-schedule-preview-dialog.component';
import { FiledCopiesDialogComponent } from './components/dialogs/filed-copies-dialog/filed-copies-dialog.component';
import { ImanageAuthComponent } from './components/imanage-auth/imanage-auth.component';
import { ShowImanageDialogComponent } from './components/dialogs/show-imanage-dialog/show-imanage-dialog.component';
import { LandRegistryDialogQuery, LandRegistryDialogService, LandRegistryDialogStore } from './store/land-registry-dialog';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { ThemesResolve } from './resolvers/themes.resolve';
import { UploadSearchesDialogComponent } from './components/dialogs/upload-searches-dialog/upload-searches-dialog.component';
import { SearchFilesTableComponent } from './components/search-files-table/search-files-table.component';
import { SearchFilesApi } from './api/search-files.api';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { RosApi } from './api/ros.api';
import { SearchToolsSideMenuComponent } from './components/search-tools-side-menu/search-tools-side-menu.component';
import { FormsModule } from '@angular/forms';
import { ToggleModule } from '@shared/components/toggle-control/toggle.module';
import { MapControlsComponent } from './components/land-registry-map-search/map-controls/map-controls.component';
import { MapSidebarComponent } from './components/land-registry-map-search/map-sidebar/map-sidebar.component';
import { MapUtilsService } from './services/map-utils.service';
import { MapSidebarDetailsComponent } from './components/land-registry-map-search/map-sidebar-details/map-sidebar-details.component';
import { MapDetailsListItemComponent } from './components/land-registry-map-search/map-details-list-item/map-details-list-item.component';
import { MapDetailsListComponent } from './components/land-registry-map-search/map-details-list/map-details-list.component';
import { LandRegistryMapSearchComponent } from './components/land-registry-map-search/land-registry-map-search/land-registry-map-search.component';
import { SamModule } from './modules/sam/sam.module';
import { MapSearchModule } from './modules/map-search/map-search.module';
import { PurchaseResultDetailsDialogComponent } from './components/dialogs/purchase-result-details-dialog/purchase-result-details-dialog.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { FiledCopiesDialogV2Component } from './components/dialogs/filed-copies-dialog-v2/filed-copies-dialog-v2.component';
import { FiledCopyUploadButtonComponent } from './components/buttons/filed-copy-analyse-button/filed-copy-upload-button.component';
import { ScheduleFilesUploadButtonComponent } from './components/buttons/schedule-files-upload-button/schedule-files-upload-button.component';
import { FiledCopyButtonComponent } from './components/buttons/filed-copy-button/filed-copy-button.component';
import { LinkedDocumentAnalysisService } from './services/linked-document-analysis.service';
import { SchedulesTableService } from './services/schedules-table.service';
import { SchedulesService } from './store/schedules/schedules.service';
import { FiledCopiesModule } from './modules/filed-copies/filed-copies.module';
import { TitlesRoutingModule } from './routing/titles-routing.module';
import { AnimatedButtonFiveStatesModule } from '@shared/components/animated-button-five-states/animated-button-five-states.module';
import { FiledCopiesTourModule } from './modules/filed-copies-tour/filed-copies-tour.module';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TeamworkModule } from './modules/teamwork/teamwork.module';

@NgModule({
    declarations: [
        TitleRegistersComponent,
        DocumentsComponent,
        DropAreaComponent,
        ReportGenerationDialogComponent,
        DownloadReportDialogComponent,
        TitlesUploadComponent,
        SidenavComponent,
        PurchasedPageComponent,
        ReportsPageComponent,
        PurchasedFilesTableComponent,
        PurchaseDownloadButtonComponent,
        SelectionToolbarComponent,
        ReportsTableComponent,
        LandRegistryDialogComponent,
        LandRegistrySearchComponent,
        LandRegistryResultsTableComponent,
        PurchaseTitlesButtonComponent,
        DownloadReportButtonComponent,
        SchedulePurchasesButtonComponent,
        TitlesSchedulesTableComponent,
        SchedulesTableComponent,
        DownloadSchedulePreviewDialogComponent,
        FiledCopiesDialogComponent,
        FiledCopiesDialogV2Component,
        ImanageAuthComponent,
        ShowImanageDialogComponent,
        SearchToolsSideMenuComponent,
        PurchaseResultDetailsDialogComponent,
        MapDetailsListComponent,
        MapDetailsListItemComponent,
        MapSidebarDetailsComponent,
        MapControlsComponent,
        MapSidebarComponent,
        LandRegistryMapSearchComponent,
        UploadSearchesDialogComponent,
        SearchFilesTableComponent,
        ScheduleFilesUploadButtonComponent,
        FiledCopyButtonComponent,
        FiledCopyUploadButtonComponent,
    ],
    imports: [
        SharedModule,
        FormsModule,
        ToggleModule,
        OnboardingModule,
        MatAutocompleteModule,
        DragDropModule,
        MatExpansionModule,
        TitlesRoutingModule,
        AnimatedButtonFiveStatesModule,
        FiledCopiesModule,
        SamModule,
        MapSearchModule,
        FiledCopiesTourModule,
        MatProgressSpinnerModule,
        TeamworkModule,
    ],
    providers: [
        { provide: 'appName', useValue: 'titles' },
        HttpStatusesHandlerService,
        SearchFilesApi,
        SearchFilesStore,
        SearchFilesService,
        FiledCopyApi,
        FiledCopiesUploadQuery,
        FiledCopiesUploadStore,
        FiledCopiesUploadService,
        ScheduleFilesUploadStore,
        ScheduleFilesUploadQuery,
        ScheduleFilesUploadService,
        SearchFilesQuery,
        PurchaseApi,
        TitlesResolve,
        ThemesResolve,
        ThemesService,
        DocumentApi,
        FolderApi,
        ReportApi,
        HmlrApi,
        BookmarksApi,
        RosApi,
        DocumentsService,
        PurchaseService,
        ReportService,
        ImanageService,
        FolderService,
        GeneratedReportsService,
        LandRegistryService,
        HmLandRegistryService,
        ScotlandLandRegistryService,
        ThemesService,
        NotifyCountersService,
        RefreshService,
        ReportGenerationHandlerService,
        DocumentsStore,
        DocumentsQuery,
        FailedDocumentsStore,
        FailedDocumentsService,
        FailedDocumentsQuery,
        FailedPurchasesStore,
        FailedPurchasesQuery,
        ReportOptionsStore,
        ReportOptionsQuery,
        PurchasedFilesQuery,
        PurchasedFilesStore,
        PurchasedSelectedFilesStore,
        PurchasedSelectedFilesQuery,
        GeneratedReportsQuery,
        GeneratedReportsStore,
        SearchResultsStore,
        SearchResultsQuery,
        NotifyCountersStore,
        NotifyCountersQuery,
        SchedulesApi,
        SchedulesStore,
        SchedulesService,
        SchedulesQuery,
        SchedulesResolve,
        FiledCopiesDialogQuery,
        FiledCopiesDialogStore,
        FiledCopiesDialogService,
        MapSearchStore,
        MapSearchQuery,
        MapUtilsService,
        MapSearchService,
        LandRegistryDialogStore,
        LandRegistryDialogQuery,
        LandRegistryDialogService,
        LinkedDocumentAnalysisService,
        SchedulesTableService,
    ],
    exports: [
        SidenavComponent,
    ],
})
export class TitlesModule {
}
