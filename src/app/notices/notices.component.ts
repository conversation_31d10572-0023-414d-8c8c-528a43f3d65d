import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '@shared/components/dialogs/confirm-dialog/confirm-dialog.component';
import { CREATE_NEW_NOTICE_CONFIRM_DATA, routingNotice } from 'app/core/constants';
import { Router } from '@angular/router';
import { Logo } from '@core/types';
import { ProfileService } from '@services';
import { NoticeService } from './services/notice.service';
import { SideNavCounters } from './types/side-nav-counters.type';
import { SideNavCountersService } from './store/side-nav-counters/side-nav-counters.service';
import { SideNavCountersQuery } from './store/side-nav-counters/side-nav-counters.query';
import { ProjectDetailsQuery } from '../project-details/stores/project-details/project-details.query';
import { ProjectsService } from '../project-details/services/projects.service';
import { ProjectDetails } from '../project-details/types/project-details.type';

const litig8Logo: Logo = {
    defaultLogo: true,
    icon: 'assets/images/avail-notice-logo.svg',
    iconWidth: 141,
    iconHeight: 20,
};

@Component({
    selector: 'avl-notices',
    templateUrl: './notices.component.html',
    styleUrls: ['./notices.component.scss'],
})

export class NoticesComponent implements OnInit {
    public readonly routingNotice = routingNotice;
    public folderDetails$: Observable<ProjectDetails>;
    public folderIsNotCreated$: Observable<boolean>;
    public logo = litig8Logo;
    public counters$: Observable<SideNavCounters>;

    constructor(
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly profileService: ProfileService,
        private readonly dialog: MatDialog,
        private readonly router: Router,
        private readonly countersService: SideNavCountersService,
        private readonly countersQuery: SideNavCountersQuery,
        private readonly noticeService: NoticeService,
        private readonly projectsService: ProjectsService,
    ) {
    }

    public ngOnInit(): void {
        this.counters$ = this.countersQuery.select();
        this.folderDetails$ = this.projectDetailsQuery.selectDetails();
        this.folderIsNotCreated$ = this.projectDetailsQuery.select((state) => !state.id);
        this.profileService.loadConfig();
        this.localSidenavCounters();
    }

    public createNewProject(): void {
        const dialogRef = this.dialog.open(ConfirmDialogComponent,
            { panelClass: 'confirm-dialog', data: CREATE_NEW_NOTICE_CONFIRM_DATA });

        dialogRef.afterClosed().subscribe((isConfirm) => {
            if (!isConfirm) {
                return;
            }
            this.resetViewState();
        });
    }

    public onFolderDetailsOpen(): void {
        this.noticeService.openProjectDetailsDialogWithData();
    }

    public localSidenavCounters(): void {
        this.countersService.load();
    }

    private resetViewState(): void {
        this.noticeService.clearState();
        this.projectsService.reset();
        this.router.navigate([routingNotice]);
    }
}
