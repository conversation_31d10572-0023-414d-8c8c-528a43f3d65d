import { Injectable } from '@angular/core';
import { SideNavCountersStore } from './side-nav-counters.store';
import { BookmarksApi } from '../../api/bookmarks.api';
import { NoticeProject } from '../../modules/teamwork/types/notice-project.type';
import { TeamworkApi } from '../../../teamwork-common/api/teamwork.api';

@Injectable()
export class SideNavCountersService {

    constructor(
        private readonly sideNavCountersStore: SideNavCountersStore,
        private readonly bookmarksApi: BookmarksApi,
        private readonly teamworkApi: TeamworkApi<NoticeProject>,
    ) {
    }

    public load(): void {
        this.bookmarksApi.getActive()
            .subscribe((statistic) => {
                this.sideNavCountersStore.update({
                    previousActiveProjects: statistic.active,
                });
            });
        this.teamworkApi.getMetrics()
            .subscribe((statistic) => {
                this.sideNavCountersStore.update({
                    teamProjects: statistic.active,
                });
            });
    }
}
