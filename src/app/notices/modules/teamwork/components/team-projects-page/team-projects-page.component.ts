import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NoticeProject } from '../../types/notice-project.type';
import { TeamProjectsService } from '../../../../../teamwork-common/stores/team-projects.service';
import { TeamProjectsQuery } from '../../../../../teamwork-common/stores/team-projects.query';
import { BaseTeamProjectsPageComponent } from '../../../../../teamwork-common/components/base-team-projects-page.component';
import { TeamProjectsTableComponent } from '../team-projects-table/team-projects-table.component';

@Component({
    selector: 'avl-team-projects-page',
    templateUrl: './team-projects-page.component.html',
    styleUrls: ['./team-projects-page.component.scss'],
})
export class TeamProjectsPageComponent extends BaseTeamProjectsPageComponent<NoticeProject> implements OnInit, OnDestroy {
    @ViewChild(TeamProjectsTableComponent, { static: false })
    public tableComponent: TeamProjectsTableComponent;


    constructor(
        teamProjectsService: TeamProjectsService<NoticeProject>,
        teamProjectsQuery: TeamProjectsQuery<NoticeProject>,
        private readonly router: Router,
    ) {
        super(teamProjectsService, teamProjectsQuery);
    }

    public ngOnInit(): void {
        super.ngOnInit();
    }

    public ngOnDestroy(): void {
        super.ngOnDestroy();
    }

    public onProjectOpened(item: NoticeProject): void {
        void this.router.navigate(['notice/upload'], { queryParams: { fid: item.id } });
    }

    protected resetPaginationToFirstPage(): void {
        this.tableComponent.resetToFirstPage();
    }

    protected normalizeSortOption(sort: string | undefined): string | null {
        return sort === 'notices' ? 'documentCount' : sort;
    }
}
