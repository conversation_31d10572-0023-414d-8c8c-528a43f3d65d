import { AfterViewInit, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';

import { takeUntil, tap } from 'rxjs/operators';
import { merge, Subject } from 'rxjs';

import { TableRowsAmountService } from '@services';
import { ICollectionHeightOffset, ICollectionPageEvent, IPagination } from '@core/types';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { NoticeProject } from '../../types/notice-project.type';

const tableHeightOffset: ICollectionHeightOffset = {
    header: 64,
    tableHeader: 54,
    tableHeaderColumns: 44,
    paginator: 56,
    footerButton: 0,
};
const rowHeightPx = 59;

@Component({
    selector: 'avl-team-projects-table',
    templateUrl: './team-projects-table.component.html',
    styleUrls: ['./team-projects-table.component.scss'],
    animations: [
        trigger('detailExpand', [
            state('collapsed', style({ height: '0', minHeight: '0', padding: 0 })),
            state('expanded', style({ height: '*', display: '*' })),
            transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
        ]),
    ],
})
export class TeamProjectsTableComponent implements OnInit, OnChanges, AfterViewInit, OnDestroy {
    @Input()
    public isLoading = true;

    @Input()
    public projects: NoticeProject[] = [];

    @Input()
    public pagination: IPagination = { totalCount: 0 };

    @Output()
    public pageChanged = new EventEmitter<ICollectionPageEvent>();

    @Output()
    public projectOpened = new EventEmitter<NoticeProject>();

    @ViewChild(MatSort, { static: true })
    public sort: MatSort;

    @ViewChild(MatPaginator, { static: true })
    public paginator: MatPaginator;

    public displayedColumns: string[] = ['projectName', 'owner', 'lastOpenedAt', 'notices', 'open'];
    public tableSource = new MatTableDataSource<NoticeProject>([]);
    public tableRowsAmount: number;

    private readonly destroy$ = new Subject<void>();

    constructor(
        private readonly tableRowsAmountService: TableRowsAmountService,
    ) {
    }

    public ngOnInit(): void {
        this.calculateRowsAmount();
        this.resetToFirstPage();
    }

    public ngOnChanges(): void {
        this.tableSource.data = this.projects;
    }

    public ngAfterViewInit(): void {
        this.sort.sortChange.subscribe(() => this.paginator.pageIndex = 0);
        this.setupPageChangeListener();
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.unsubscribe();
    }

    public openProject(item: NoticeProject): void {
        this.projectOpened.emit(item);
    }

    public resetToFirstPage(): void {
        this.paginator.pageIndex = 0;
        this.paginator.firstPage();
        this.pageChanged.emit({
            pageIndex: 0,
            pageSize: this.tableRowsAmount,
            sort: this.sort.active,
            order: this.sort.direction,
        });
    }

    public rowTrack(index: number, item: NoticeProject): string {
        return item.id;
    }

    private calculateRowsAmount(): void {
        this.tableRowsAmount = this.tableRowsAmountService
            .calculateRowsAmount(tableHeightOffset, rowHeightPx);
    }

    private setupPageChangeListener(): void {
        merge(this.sort.sortChange, this.paginator.page)
            .pipe(
                takeUntil(this.destroy$),
                tap(() => {
                    this.pageChanged.emit({
                        pageIndex: this.paginator.pageIndex,
                        pageSize: this.paginator.pageSize,
                        sort: this.sort.active,
                        order: this.sort.direction,
                    });
                }))
            .subscribe();
    }
}
