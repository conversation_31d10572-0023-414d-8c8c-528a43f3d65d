:host {
    display: flex;
    flex: 1;
    flex-direction: column;
}

.team-projects-table-container {
    ::ng-deep &.collection__table-container .loading-table {
        &__row {
            height: 57px !important;
        }
    }

    ::ng-deep .mat-column {
        &-projectName {
            width: 40%;
            white-space: nowrap;
        }

        &-owner {
            width: 35%;
        }

        &-lastOpenedAt {
            width: 15%;
            min-width: 160px;
        }

        &-notices {
            width: 8%;
            min-width: 60px;
            text-align: center;

            .loading-table__loader {
                margin: auto;
            }

            .mat-sort-header-container {
                justify-content: center;
            }
        }

        &-open {
            width: 1%;
            padding-left: 0;
            min-width: 88px;
            text-align: right;

            &.mat-header-cell {
                min-width: 112px;
            }
        }
    }
}

.project-info-column {
    display: flex;
    flex-direction: column;
    white-space: nowrap;
}

.project-name {
    font-size: 14px;
    line-height: 22px;
}

.matter-number {
    font-size: 12px;
    line-height: 20px;
    opacity: 0.5;
}

.open-project-btn {
    padding: 8px 10px;
    height: 32px;
    border-radius: 3px;
    width: 88px;
}
