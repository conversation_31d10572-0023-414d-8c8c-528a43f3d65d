<div class="collection__table-container team-projects-table-container">
    <div class="collection__table-wrap">
        <table
            class="collection__table"
            mat-table
            matSort
            [trackBy]="rowTrack"
            [dataSource]="tableSource"
        >
            <!-- Name Column -->
            <ng-container matColumnDef="projectName">
                <th
                    mat-header-cell
                    mat-sort-header
                    *matHeaderCellDef
                >
                    Project Name
                </th>
                <td
                    mat-cell
                    *matCellDef="let row"
                >
                    <div class="project-info-column">
                        <span class="project-name">{{ row.projectName || '-' }}</span>
                        <span class="matter-number">{{ row.matterNumber || '-' }}</span>
                    </div>
                </td>
            </ng-container>

            <!-- Owner's Email Column -->
            <ng-container matColumnDef="owner">
                <th
                    mat-header-cell
                    mat-sort-header
                    *matHeaderCellDef
                >
                    Owner
                </th>
                <td
                    mat-cell
                    *matCellDef="let row"
                >
                    {{ row.owner || '-' }}
                </td>
            </ng-container>

            <!-- Last Updated Column -->
            <ng-container matColumnDef="lastOpenedAt">
                <th
                    mat-header-cell
                    mat-sort-header
                    *matHeaderCellDef
                >
                    Last Updated
                </th>
                <td
                    mat-cell
                    *matCellDef="let row"
                >
                    {{ (row.lastOpenedAt | date:'d MMMM y') || '-' }}
                </td>
            </ng-container>

            <!-- Notices Column -->
            <ng-container matColumnDef="notices">
                <th
                    mat-header-cell
                    mat-sort-header
                    *matHeaderCellDef
                    class="center"
                >
                    Notices
                </th>
                <td
                    mat-cell
                    *matCellDef="let row"
                >
                    {{ row.documentCount ?? '-' }}
                </td>
            </ng-container>

            <!-- Open Column -->
            <ng-container matColumnDef="open">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                ></th>
                <td
                    mat-cell
                    *matCellDef="let row"
                >
                    <button
                        class="open-project-btn avl-btn avl-btn--notice-yellow"
                        (click)="openProject(row)"
                    >
                        Open
                    </button>
                </td>
            </ng-container>

            <tr
                mat-header-row
                *matHeaderRowDef="displayedColumns"
            ></tr>
            <tr
                mat-row
                [hidden]="isLoading"
                *matRowDef="let row; columns: displayedColumns;"
            ></tr>
        </table>
        <avl-table-loading-placeholder
            *ngIf="isLoading"
            [columns]="displayedColumns"
            [size]="tableRowsAmount"
        ></avl-table-loading-placeholder>
    </div>
    <avl-table-no-data-disclaimer
        *ngIf="!isLoading && !projects.length"
        iconName="file-chart-grey"
        message="No team projects"
    ></avl-table-no-data-disclaimer>
    <mat-paginator
        [length]="pagination.totalCount"
        [class.hidden]="!projects.length"
        [pageSize]="tableRowsAmount"
        [hidePageSize]="true"
    ></mat-paginator>
</div>
