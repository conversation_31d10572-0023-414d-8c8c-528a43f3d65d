@use 'assets/sass/variables' as *;

.notice-area {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    background: #ebebeb;
    border: 2px dashed $silver;
    border-radius: 5px;
    transition: border-color 0.5s ease;
    overflow: hidden;

    &.scaled {
        border: none;
        background-color: #fff;
        animation: slideUp 0.5s ease forwards;

        .notice-area__image {
            right: 165px;
            top: 74px;
        }
    }

    &.file-over {
        border-color: $notice-yellow;
        background-color: #fff;
    }

    &__image {
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translate(-50%, 0);

        .mat-icon {
            width: 48px;
            height: 48px;
        }

        &--error {
            left: 30px;
            transform: translate(0, 0);

            & .error-icon {
                &__border {
                    stroke: $notice-yellow;
                }

                &__point,
                &__stick {
                    fill: $notice-yellow;
                }
            }
        }
    }

    &__main {
        position: absolute;
        width: 100%;
        top: 55%;
        padding: 30px;
        text-align: center;
        transform: translateY(-50%);
    }

    &__upload-input {
        position: absolute;
        visibility: hidden;
    }

    &__upload-btn {
        background-color: $notice-yellow;

        &:hover {
            background-color: rgba($notice-yellow, 0.7);
        }
    }

    &__title {
        color: #505050;
        font-size: 16px;
        padding-top: 16px;
    }

    &__description {
        font-size: 14px;
        line-height: 18px;
        margin-top: 4px;
        margin-bottom: 16px;
    }

    &__progress-info {
        padding-top: 3px;
        opacity: 0;
        position: absolute;
        left: calc(50% + -20px);
        top: 50%;
        transform: translate(0, -50%);
        visibility: hidden;
        max-width: 42%;

        &.is-visible {
            animation: showing 0.5s ease forwards;
        }

        @media screen and (min-width: 1350px) {
            max-width: 400px;
        }
    }

    &__sub-title {
        color: $black;
        font-size: 16px;
        line-height: 18px;
        margin-bottom: 8px;
        overflow-x: hidden;
        text-overflow: ellipsis;
    }

    &__percentage {
        color: $dark-gray;
        font-size: 12px;
        line-height: 16px;
    }

    &__default {
        margin-top: 30px;

        &.fade-out-down {
            animation: fadeOutDown 0.5s ease forwards;
        }

        &.fade-in-up {
            animation: fadeInUp 0.5s ease forwards;
        }
    }

    &__progress-bar {
        position: absolute !important;
        bottom: 0;
        left: 0;
    }

    &__cancel {
        cursor: pointer;
        position: absolute;
        right: 47px;
        top: 88px;
        width: 24px;
        height: 24px;
        border: none;
        background-color: transparent;

        &:hover {
            .notice-area__cancel-icon {
                fill: #505050;

                &.white {
                    fill: #fff;
                }
            }
        }
    }

    &__cancel-icon {
        fill: $gray;
        transition: fill 0.2s;

        &.white {
            fill: #fff;
        }
    }
}

.loading-spinner {
    position: relative;
    width: 48px;
    margin: auto;

    &__arrow {
        position: absolute;
        inset: 0;
    }
}

.uploading {
    animation: slide 0.5s ease forwards;
}

@keyframes slide {
    from {
        left: 50%;
    }

    to {
        left: 30px;
        transform: translate(0);
    }
}

@keyframes fadeOutDown {
    from {
        visibility: visible;
        opacity: 1;
    }

    to {
        opacity: 0;
        visibility: hidden;
        transform: translate3d(0, 20%, 0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        visibility: hidden;
        transform: translate3d(0, 20%, 0);
    }

    to {
        opacity: 1;
        visibility: visible;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideUp {
    from {
        max-height: 220px;
    }

    to {
        max-height: 108px;
    }
}

@keyframes showing {
    0% {
        visibility: hidden;
        opacity: 0;
    }

    98% {
        visibility: hidden;
        opacity: 0;
    }

    100% {
        visibility: visible;
        opacity: 1;
    }
}
