@use 'assets/sass/variables' as *;

.head-bg {
    width: 304px;
}

.wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding-top: 0;
}

.buttons {
    height: 100%;
    padding: 0 8px;
    font-weight: 500;
}

.button {
    margin: 8px 0;
    display: flex;
    gap: 35px;
    justify-content: flex-start;
    align-items: center;
    height: 48px;
    width: 100%;
    padding: 0 12px;
    border-radius: 4px;
    border: none;
    background-color: #fff;
    transition: all 0.2s linear;
    cursor: pointer;
    font-size: 14px;
    line-height: 24px;
    font-weight: 500;
    font-family: Roboto, 'Helvetica Neue', sans-serif;
    color: rgba(0, 0, 0, 0.87);

    &:hover {
        box-shadow: 0 4px 8px rgba(48, 79, 254, 0.04);
        background-color: rgba($notice-yellow, 0.24);
    }
}

.button-icon {
    color: $notice-yellow;
    --icon-color: #{$notice-yellow};
    --icon-stroke-width: 2;
    width: 20px;
    height: 20px;
}

.apps {
    height: 150px;
}

.position-relative {
    position: relative;
}

.counter-box {
    position: absolute;
    top: 12px;
    right: 12px;
    min-width: 24px;
    padding: 0 3px;
    height: 24px;
    background-color: $notice-yellow;
    color: #fff;
    line-height: 24px;
    text-align: center;
    border-radius: 3px;
}
