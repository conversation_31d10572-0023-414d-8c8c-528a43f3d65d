<div class="wrapper">
    <img
        class="head-bg"
        src="/assets/images/avail-notice-sidenav-bg.png"
        alt="avail notice sidenav background"
    >
    <div class="buttons">
        <button
            class="button"
            (click)="onCreateNewProject()"
        >
            <mat-icon
                class="button-icon"
                svgIcon="plus-box-without-paddings"
            ></mat-icon>
            <span>New Project</span>
        </button>
        <a
            class="button position-relative"
            routerLink="previous-projects"
            (click)="close()"
        >
            <mat-icon
                class="button-icon"
                svgIcon="file-chart-rounded"
            ></mat-icon>
            <span>Previous Projects</span>
            <span class="counter-box">{{ (counters | async)?.previousActiveProjects || 0 }}</span>
        </a>
        <a
            *ngIf="isTeamworkEnabled$ | async"
            class="button position-relative"
            routerLink="team-projects"
            (click)="close()"
        >
            <mat-icon
                class="button-icon"
                svgIcon="teamwork"
            ></mat-icon>
            <span>Team Projects</span>
            <div
                *ngIf="(counters | async)?.teamProjects"
                class="counter-box"
            >
                {{ (counters | async)?.teamProjects }}
            </div>
        </a>
    </div>

    <avl-other-tools
        class="apps"
        [excludeTool]="tools.notice"
    ></avl-other-tools>
</div>
