@use 'assets/sass/variables' as *;

:host {
    display: flex;
    flex: 1;
    flex-direction: column;

    .collection__table-container {
        ::ng-deep .loading-table__loader {
            background: linear-gradient(90deg, #e3e3e3 0%, #f5f5f5 50%, #e3e3e3 100%);
            background-size: 600% 100%;
            animation: 3s ease infinite pulse-gradient;
        }
    }
}

.folders-table {
    &__open-folder-btn {
        padding: 8px 10px;
        height: 32px;
        width: 100%;
        border-radius: 3px;
    }

    &__header-th {
        ::ng-deep {
            .mat-sort-header-container {
                letter-spacing: 1px;
            }
        }

        &--center ::ng-deep {
            .mat-sort-header-container {
                justify-content: center;
            }

            .mat-sort-header-content {
                margin-left: 18px;
            }
        }
    }

    &__row {
        font-size: 13px;
    }

    &__cell {
        &--center {
            text-align: center;
        }
    }

    &__open-folder-btn {
        width: 88px;
    }

    ::ng-deep .mat-column {
        &-project-name {
            width: 18%;
        }

        &-matter-number {
            width: 18%;
            padding: 0 !important;
        }

        &-created-at {
            width: 15%;
            padding: 0 !important;
        }

        &-last-opened-at {
            width: 15%;
            padding: 0 !important;
        }

        &-open {
            width: 1%;
            padding-left: 0;
            min-width: 88px;
            text-align: right;

            &.mat-header-cell {
                min-width: 112px;
            }
        }
    }

    ::ng-deep {
        .mat-paginator-navigation-previous {
            margin-right: 6px;
        }

        .mat-paginator-navigation-next {
            margin-left: 6px;
        }
    }
}

.collection__table {
    font-family: 'Roboto', sans-serif;
}

@keyframes pulse-gradient {
    0% {
        background-position: 0 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}
