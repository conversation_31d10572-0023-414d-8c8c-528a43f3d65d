import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';

import { NoticeDocType } from 'app/notices/enums';
import { NoticeService } from 'app/notices/services/notice.service';
import { EMPTY, iif, Observable, Subject } from 'rxjs';
import { AlertDialogComponent } from '@shared/components/dialogs/alert-dialog/alert-dialog.component';
import { NoticeGenerationDialogComponent } from '../notice-generation-dialog/notice-generation-dialog.component';
import { switchMap, tap } from 'rxjs/operators';
import { ActivatedRoute, Router } from '@angular/router';
import { SelectClientDialogComponent } from '../select-client-dialog/select-client-dialog.component';
import { NoticeDocumentsQuery } from 'app/notices/store/notice-documents';

import { DoneDialogComponent } from '@shared/components/dialogs/done-dialog/done-dialog.component';
import { fadeRowAnimation } from 'app/core/animations/fade.animation';
import { SOMETHING_GONE_WRONG } from '@constants';
import { INoticeDocument } from '../../types';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';
import { noticeProjectDetailsOptions } from '../../constants/notice-project-details-dialog-options.constant';
import { ProjectDetailsDialogService } from '../../../project-details/services/project-details-dialog.service';
import { LoggerService } from '@services';
import { NoticeAreaComponent } from '../notice-area/notice-area.component';

@Component({
    selector: 'avl-notice-documents',
    templateUrl: './notice-documents.component.html',
    styleUrls: ['./notice-documents.component.scss'],
    animations: [fadeRowAnimation],
})
export class NoticeDocumentsComponent implements OnInit {
    public readonly uploadCanceled$ = new Subject<void>();
    public documentTypes = NoticeDocType;
    public folderId$: Observable<string>;
    public isButtonVisible$: Observable<boolean>;
    public isLoading$: Observable<boolean>;
    public readyDocuments$: Observable<{ [p: string]: INoticeDocument }>;

    constructor(
        private readonly noticeDocumentsQuery: NoticeDocumentsQuery,
        private readonly noticeService: NoticeService,
        private readonly dialog: MatDialog,
        private readonly router: Router,
        private readonly route: ActivatedRoute,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly projectDetailsDialogService: ProjectDetailsDialogService,
        private readonly log: LoggerService,
    ) {
    }

    public ngOnInit(): void {
        this.noticeService.restoreFolderIdUrlQueryParam();
        this.folderId$ = this.projectDetailsQuery.projectId$;
        this.isLoading$ = this.noticeDocumentsQuery.selectLoading();
        this.isButtonVisible$ = this.noticeDocumentsQuery.isLeaseAndTitleProcessed();
        this.readyDocuments$ = this.noticeDocumentsQuery.getMappedByType();
    }

    public onFileAdded(): void {
        const isProjectCreated = this.projectDetailsQuery.isProjectCreated;

        if (!isProjectCreated) {
            this.projectDetailsDialogService.show(noticeProjectDetailsOptions)
                .then((result) => {
                    if (result.isClosedByUser) {
                        this.uploadCanceled$.next();
                        return;
                    }

                    this.navigateToNewFolder(result.project.id);
                })
                .catch((error) => {
                    this.log.error(error);
                    this.uploadCanceled$.next();
                });
        }
    }

    public onUploadingStarted(uploadId: string, fileName: string, type: NoticeDocType): void {
        this.noticeService.addTemporaryDocument(uploadId, type, fileName);
        this.noticeService.setIsLoading(true);
    }

    public onFileUploaded(fileId: string, uploadId: string): void {
        this.noticeService.bindUploadIdWithFileId(uploadId, fileId);
        this.noticeService.startPolling();
    }

    public openGenerateNoticeDialog(url: string): void {
        const dialogRef = this.dialog.open(NoticeGenerationDialogComponent,
            {
                data: { url },
                panelClass: 'report-dialog',
                width: '400px',
                disableClose: true,
            },
        );

        dialogRef.afterClosed().subscribe((result: string) => {
            switch (result) {
                case 'success':
                    this.openDoneDialog();
                    break;
                case 'error':
                    this.openAlertDialog(false, SOMETHING_GONE_WRONG);
                    break;
            }
        });
    }

    public openSelectClientDialog(): void {
        const dialogRef = this.dialog.open(SelectClientDialogComponent, { panelClass: ['report-dialog'], width: '370px' });
        dialogRef.afterClosed()
            .pipe(
                switchMap((type: string) => {
                    const folderId = this.projectDetailsQuery.getValue().id;
                    return iif(() => !!type, this.noticeService.generateNotice(folderId, type), EMPTY);
                }),
                tap((url: string) => this.openGenerateNoticeDialog(url)),
            )
            .subscribe();
    }

    public onFileRemoved(documentId: string, component: NoticeAreaComponent): void {
        const folderId = this.projectDetailsQuery.getValue().id;
        this.noticeService.removeDocument(folderId, documentId)
            .then((isRemoved) => {
                if (isRemoved) {
                    component.clearState();
                }
            });
    }

    private navigateToNewFolder(folderId: string): void {
        this.router.navigate([], {
            relativeTo: this.route,
            queryParams: { fid: folderId },
            queryParamsHandling: 'merge',
        });
    }

    private openDoneDialog(): void {
        this.dialog.open(DoneDialogComponent, {
            panelClass: ['report-dialog', 'border-radius-8px'],
            width: '440px',
            disableClose: true,
            data: { title: 'Notices downloaded successfully' },
        });
    }

    private openAlertDialog(withAfterClose?: boolean, errorData?: { title: string; message: string }): void {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const dialogRef = this.dialog.open(AlertDialogComponent, {
            panelClass: 'report-dialog',
            width: '400px',
            data: errorData,
        });

        if (withAfterClose) {
            // dialogRef.afterClosed().subscribe(_ => this.openSelectReportDialog());
        }
    }
}
