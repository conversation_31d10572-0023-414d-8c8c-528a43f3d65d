@use 'assets/sass/variables' as *;

.banner {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: space-between;
    background-color: rgb(255, 246, 241);
    width: 100%;
    padding: 15px 25px;
    border-radius: 5px;

    font-family: 'Arial', sans-serif;
    line-height: 14px;
}

.message {
    color: $dark-gray;
    font-size: 14px;
    line-height: 18px;
}

::ng-deep .br-height-4px br {
    content: '';
    display: block;
    margin-top: 4px;
}

.url-text {
    color: $notice-yellow;
    font-weight: bold;
    text-transform: uppercase;
    text-decoration: none;
    text-decoration-skip-ink: auto;
    outline: none;
    text-wrap: nowrap;
    border-bottom: 2px solid $notice-yellow;

    transition:
        color,
        border-bottom-color 0.2s ease-in-out;
}

.url-text:hover {
    color: rgba($notice-yellow, 0.7);
    border-bottom-color: rgba($notice-yellow, 0.7);
}
