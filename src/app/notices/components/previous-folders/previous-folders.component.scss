@use 'assets/sass/variables' as *;

:host {
    flex: 1;
}

.previous-folders {
    min-height: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.table {
    flex: 1;
    border: 1px solid #dcdcdc;
    border-radius: $primary-border-radius;
    background-color: white;

    ::ng-deep .collection__table-wrap {
        border-radius: $primary-border-radius;
    }
}

::ng-deep .previous-folders__search {
    .avl-search {
        gap: 5px;

        .avl-search__input {
            &::placeholder {
                color: #96969f;
            }
        }

        mat-icon {
            --icon-color: #96969f;
        }
    }
}
