import { NEVER, Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router } from '@angular/router';
import { catchError } from 'rxjs/operators';
import { NoticeService } from '../services/notice.service';
import { INoticeDocument } from '../types';
import { routingNotice } from '@constants';
import { LoggerService } from '@services';
import { ProjectDetailsQuery } from '../../project-details/stores/project-details/project-details.query';

@Injectable()
export class NoticesResolver implements Resolve<INoticeDocument[]> {

    constructor(
        private readonly noticeService: NoticeService,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly router: Router,
        private readonly log: LoggerService,
    ) {
    }

    public resolve(route: ActivatedRouteSnapshot): Observable<INoticeDocument[]> {
        this.noticeService.clearDocumentsStore();

        const id = route.queryParams.fid || this.projectDetailsQuery.projectId;
        if (!id) {
            return;
        }

        return this.noticeService.loadFolderStatus(id)
            .pipe(
                catchError((error) => {
                    this.log.warn(error);
                    this.router.navigate([routingNotice]);

                    return NEVER;
                }),
            );
    }
}
