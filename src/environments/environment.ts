// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.production.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
    production: false,
    dev: true,
    forceLogLevelDebug: true,
    isRosEnabled: true,
    isOneClickFiledCopiesPurchaseEnabled: true,
    isFiledCopiesUploadEnabled: true,
    isSamEnabled: true,
    isMapSearchEnabled: true,
    forceImanageEnabled: false,
    authIntercept: true,
    basicAuth: false,
    authGuard: true,
    api: 'api/titles',
    cloudFunctions: 'https://europe-west2-internal-identity.cloudfunctions.net',
    googleApiKey: 'AIzaSyDa9tu-mrQymrlWRz7j8iY46nCiamKwEmQ',
    firebase: {
        apiKey: 'AIzaSyAfoEt0phNGL5B0O_JBz-bXEBPxlLIbugc',
        authDomain: 'internal-identity.firebaseapp.com',
        // authDomain: 'a.avail.ai',
    },
    enableIntercom: false,
    intercomModule: {
        appId: 'j5hpcgz2', // from your Intercom config
        updateOnRouterChange: true, // will automatically run `update` on router event changes. Default: `false`
    },
    intercomData: {
        appId: 'j5hpcgz2',
        apiBase: 'https://api-iam.eu.intercom.io',
        // Supports all optional configuration.
        widget: {},
        customLauncherSelector: '#support',
        hideDefaultLauncher: true,
        userHash: null,
    },
    sentryData: {
        dsn: null,
        isEnabled: false,
        isTracingEnabled: false,
        tracesSampleRate: 0.3,
    },
    imanageConfig: {
        imanageUrl: 'https://cloudimanage.com',
        clientId: 'd68caacf-e36d-4d46-868a-070a394a4ef3',
        redirectUri: 'http://localhost/',
    },
    mapSearch: {
        styleUrl: '/assets/map-settings/OS_VTS_3857_Light_Dev.json',
        stylesUrlOrigin: null,
    },
    isTitleDocumentsReorderingEnabled: true,
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
